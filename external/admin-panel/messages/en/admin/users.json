{"admin": {"users": {"title": "User Management", "description": "View and manage system users", "addNew": "Add New User", "search": {"title": "Search and Filter", "description": "Search users by name, phone, or email", "placeholder": "Search users..."}, "table": {"title": "User List", "found": "users found", "headers": {"name": "Name", "phone": "Phone", "email": "Email", "status": "Status", "role": "Role", "createdAt": "Join Date", "actions": "Actions"}, "status": {"active": "Active", "inactive": "Inactive"}, "roles": {"admin": "Admin", "affiliate": "Affiliate", "user": "User"}, "actions": {"edit": "Edit", "delete": "Delete"}}}}}