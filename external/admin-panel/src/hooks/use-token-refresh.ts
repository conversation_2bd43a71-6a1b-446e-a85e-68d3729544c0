"use client";

import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useState, useCallback, useEffect } from "react";
import { isTokenExpired, isTokenExpiringSoon } from "@/lib/auth-types";
import { handleTokenRefreshError } from "@/lib/token-refresh";

interface UseTokenRefreshOptions {
  onRefreshStart?: () => void;
  onRefreshSuccess?: () => void;
  onRefreshError?: (error: string) => void;
  checkIntervalMs?: number;
  bufferMinutes?: number;
}

export function useTokenRefresh(options: UseTokenRefreshOptions = {}) {
  const { data: session, update } = useSession();
  const router = useRouter();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [refreshError, setRefreshError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const {
    onRefreshStart,
    onRefreshSuccess,
    onRefreshError,
    checkIntervalMs = 60000, // 1 minute
    bufferMinutes = 5,
  } = options;

  const handleRefreshError = useCallback(
    async (error: string) => {
      console.error("🔄 Token refresh error:", error);
      setRefreshError(error);
      onRefreshError?.(error);

      const errorHandling = handleTokenRefreshError(error);

      if (errorHandling.shouldLogout) {
        console.log("🔄 Logging out due to token refresh error");
        await signOut({ redirect: false });
        router.push("/login?expired=true");
        return;
      }

      if (errorHandling.shouldRetry && retryCount < 3) {
        console.log(`🔄 Retrying token refresh (attempt ${retryCount + 1}/3)`);
        setRetryCount((prev) => prev + 1);
        
        // Retry after a delay
        setTimeout(() => {
          checkAndRefreshToken();
        }, Math.min(1000 * Math.pow(2, retryCount), 10000)); // Exponential backoff, max 10s
      } else if (retryCount >= 3) {
        console.log("🔄 Max retry attempts reached, logging out");
        await signOut({ redirect: false });
        router.push("/login?expired=true");
      }
    },
    [retryCount, onRefreshError, router]
  );

  const checkAndRefreshToken = useCallback(async () => {
    if (!session?.accessToken || isRefreshing) {
      return;
    }

    try {
      const accessToken = session.accessToken;

      // Check if token is expired or expiring soon
      const tokenExpired = isTokenExpired(accessToken);
      const tokenExpiringSoon = isTokenExpiringSoon(accessToken, bufferMinutes);

      if (!tokenExpired && !tokenExpiringSoon) {
        // Token is still valid
        if (refreshError) {
          setRefreshError(null);
          setRetryCount(0);
        }
        return;
      }

      console.log("🔄 Token needs refresh:", { tokenExpired, tokenExpiringSoon });
      
      setIsRefreshing(true);
      onRefreshStart?.();

      // Trigger NextAuth session update which will call the JWT callback
      // The JWT callback handles the actual token refresh logic
      const updatedSession = await update();

      if (!updatedSession?.accessToken) {
        throw new Error("refresh_failed_no_token");
      }

      // Verify the new token is valid
      if (isTokenExpired(updatedSession.accessToken)) {
        throw new Error("refresh_failed_still_expired");
      }

      console.log("✅ Token refresh successful");
      setRefreshError(null);
      setRetryCount(0);
      onRefreshSuccess?.();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "unknown_error";
      await handleRefreshError(errorMessage);
    } finally {
      setIsRefreshing(false);
    }
  }, [
    session,
    isRefreshing,
    bufferMinutes,
    refreshError,
    onRefreshStart,
    onRefreshSuccess,
    update,
    handleRefreshError,
  ]);

  // Set up periodic token check
  useEffect(() => {
    if (!session?.accessToken) return;

    const interval = setInterval(() => {
      checkAndRefreshToken();
    }, checkIntervalMs);

    return () => clearInterval(interval);
  }, [session?.accessToken, checkAndRefreshToken, checkIntervalMs]);

  // Check token immediately when session changes
  useEffect(() => {
    if (session?.accessToken && !isRefreshing) {
      checkAndRefreshToken();
    }
  }, [session?.accessToken, checkAndRefreshToken, isRefreshing]);

  return {
    isRefreshing,
    refreshError,
    retryCount,
    checkAndRefreshToken,
    clearError: () => {
      setRefreshError(null);
      setRetryCount(0);
    },
  };
}

/**
 * Hook for components that need to handle token refresh errors with UI feedback
 */
export function useTokenRefreshWithToast() {
  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [toastType, setToastType] = useState<"info" | "error" | "success">("info");

  const tokenRefresh = useTokenRefresh({
    onRefreshStart: () => {
      setToastMessage("Refreshing session...");
      setToastType("info");
    },
    onRefreshSuccess: () => {
      setToastMessage("Session refreshed successfully");
      setToastType("success");
      // Clear success message after 2 seconds
      setTimeout(() => setToastMessage(null), 2000);
    },
    onRefreshError: (error) => {
      const errorHandling = handleTokenRefreshError(error);
      setToastMessage(errorHandling.message);
      setToastType("error");
    },
  });

  return {
    ...tokenRefresh,
    toastMessage,
    toastType,
    clearToast: () => setToastMessage(null),
  };
}
