import { useState, useEffect } from "react";
import { DiscountOutputDto } from "@/lib/admin/discount-types";

type FilterStatus = "all" | "ACTIVE" | "INACTIVE";

export function useFilteredDiscounts(discounts: DiscountOutputDto[]) {
  const [filter, setFilter] = useState<FilterStatus>("all");
  const [filteredDiscounts, setFilteredDiscounts] =
    useState<DiscountOutputDto[]>(discounts);

  useEffect(() => {
    if (filter === "all") {
      setFilteredDiscounts(discounts);
    } else if (filter === "ACTIVE") {
      setFilteredDiscounts(
        discounts.filter((discount) => discount.state === "ACTIVE")
      );
    } else if (filter === "INACTIVE") {
      setFilteredDiscounts(
        discounts.filter(
          (discount) =>
            discount.state === "DEACTIVE" || discount.state === "USED"
        )
      );
    }
  }, [filter, discounts]);

  return { filteredDiscounts, filter, setFilter };
}
