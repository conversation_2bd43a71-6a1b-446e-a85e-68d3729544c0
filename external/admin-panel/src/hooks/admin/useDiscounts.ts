import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";
import { getDiscounts } from "@/lib/admin/discount-api";
import {
  DiscountOutputDto,
  DiscountPaginationOutputDto,
  DiscountQueryParams,
  DiscountStatsParams,
} from "@/lib/admin/discount-types";

export function useDiscounts(initialFilters: DiscountQueryParams = {}) {
  const { data: session } = useSession();
  const t = useTranslations("admin.discount.discounts");
  const [paginationData, setPaginationData] =
    useState<DiscountPaginationOutputDto | null>(null);
  const [discounts, setDiscounts] = useState<DiscountOutputDto[]>([]);
  const [stats, setStats] = useState<DiscountStatsParams>({
    activeCount: 0,
    usedCount: 0,
    expiredCount: 0,
    totalCount: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [filters, setFilters] = useState<DiscountQueryParams>(initialFilters);

  const loadDiscounts = useCallback(
    async (queryParams: DiscountQueryParams = {}) => {
      if (!session?.accessToken) {
        setError("خطا در احراز هویت");
        setLoading(false);
        return;
      }

      setLoading(true);
      setError("");

      try {
        const params = { ...filters, ...queryParams };
        const result = await getDiscounts(session.accessToken, params);

        if (result.success && result.data) {
          setPaginationData(result.data);
          setDiscounts(result.data.data);
          setStats({
            totalCount:
              result.data.activeCount +
              result.data.usedCount +
              result.data.expiredCount,
            activeCount: result.data.activeCount,
            usedCount: result.data.usedCount,
            expiredCount: result.data.expiredCount,
          });
        } else {
          setError(result.message || t("messages.error"));
          setPaginationData(null);
          setDiscounts([]);
          setStats({
            activeCount: 0,
            usedCount: 0,
            expiredCount: 0,
            totalCount: 0,
          });
        }
      } catch (err) {
        console.error("Load discounts error:", err);
        setError(t("messages.networkError"));
        setPaginationData(null);
        setDiscounts([]);
      } finally {
        setLoading(false);
      }
    },
    [session?.accessToken, t, filters]
  );

  const updateFilters = useCallback(
    (newFilters: Partial<DiscountQueryParams>) => {
      const updatedFilters = { ...filters, ...newFilters };
      setFilters(updatedFilters);
      loadDiscounts(updatedFilters);
    },
    [filters, loadDiscounts]
  );

  const changePage = useCallback(
    (page: number) => {
      updateFilters({ page });
    },
    [updateFilters]
  );

  const changePageSize = useCallback(
    (limit: number) => {
      updateFilters({ limit, page: 1 }); // Reset to first page when changing page size
    },
    [updateFilters]
  );

  useEffect(() => {
    loadDiscounts();
  }, [loadDiscounts]);

  return {
    discounts,
    stats,
    paginationData,
    loading,
    error,
    filters,
    loadDiscounts,
    updateFilters,
    changePage,
    changePageSize,
  };
}
