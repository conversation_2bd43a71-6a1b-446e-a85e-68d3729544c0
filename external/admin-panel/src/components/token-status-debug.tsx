"use client";

import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import {
  isTokenExpired,
  isTokenExpiringSoon,
  getTokenExpiration,
} from "@/lib/auth-types";
import { useTokenRefresh } from "@/hooks/use-token-refresh";

interface TokenInfo {
  isExpired: boolean;
  isExpiringSoon: boolean;
  expirationTime: number | null;
  timeUntilExpiry: string;
  currentTime: number;
}

export default function TokenStatusDebug() {
  const { data: session } = useSession();
  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);
  const { isRefreshing, refreshError, retryCount } = useTokenRefresh();

  useEffect(() => {
    if (!session?.accessToken) {
      setTokenInfo(null);
      return;
    }

    const updateTokenInfo = () => {
      const accessToken = session.accessToken!;
      const currentTime = Math.floor(Date.now() / 1000);
      const expirationTime = getTokenExpiration(accessToken);

      let timeUntilExpiry = "Unknown";
      if (expirationTime) {
        const secondsUntilExpiry = expirationTime - currentTime;
        if (secondsUntilExpiry > 0) {
          const minutes = Math.floor(secondsUntilExpiry / 60);
          const seconds = secondsUntilExpiry % 60;
          timeUntilExpiry = `${minutes}m ${seconds}s`;
        } else {
          timeUntilExpiry = "Expired";
        }
      }

      setTokenInfo({
        isExpired: isTokenExpired(accessToken),
        isExpiringSoon: isTokenExpiringSoon(accessToken, 5),
        expirationTime,
        timeUntilExpiry,
        currentTime,
      });
    };

    updateTokenInfo();
    const interval = setInterval(updateTokenInfo, 1000);

    return () => clearInterval(interval);
  }, [session?.accessToken]);

  if (!session) {
    return (
      <div className="p-4 bg-gray-100 rounded-lg">
        <h3 className="font-bold text-gray-800">Token Status Debug</h3>
        <p className="text-gray-600">No session found</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-gray-100 rounded-lg space-y-2">
      <h3 className="font-bold text-gray-800">Token Status Debug</h3>

      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <strong>Session Status:</strong>
          <div className="text-green-600">✓ Active</div>
        </div>

        <div>
          <strong>Refresh Status:</strong>
          <div className={isRefreshing ? "text-yellow-600" : "text-green-600"}>
            {isRefreshing ? "🔄 Refreshing..." : "✓ Ready"}
          </div>
        </div>

        {tokenInfo && (
          <>
            <div>
              <strong>Token Expired:</strong>
              <div
                className={
                  tokenInfo.isExpired ? "text-red-600" : "text-green-600"
                }
              >
                {tokenInfo.isExpired ? "❌ Yes" : "✓ No"}
              </div>
            </div>

            <div>
              <strong>Expiring Soon:</strong>
              <div
                className={
                  tokenInfo.isExpiringSoon
                    ? "text-yellow-600"
                    : "text-green-600"
                }
              >
                {tokenInfo.isExpiringSoon ? "⚠️ Yes" : "✓ No"}
              </div>
            </div>

            <div>
              <strong>Time Until Expiry:</strong>
              <div
                className={
                  tokenInfo.isExpired
                    ? "text-red-600"
                    : tokenInfo.isExpiringSoon
                    ? "text-yellow-600"
                    : "text-green-600"
                }
              >
                {tokenInfo.timeUntilExpiry}
              </div>
            </div>

            <div>
              <strong>Expiration Time:</strong>
              <div className="text-gray-600">
                {tokenInfo.expirationTime
                  ? new Date(tokenInfo.expirationTime * 1000).toLocaleString()
                  : "Unknown"}
              </div>
            </div>
          </>
        )}

        {refreshError && (
          <div className="col-span-2">
            <strong>Refresh Error:</strong>
            <div className="text-red-600">{refreshError}</div>
            <div className="text-gray-600">Retry count: {retryCount}</div>
          </div>
        )}
      </div>

      <div className="mt-4 p-2 bg-white rounded text-xs">
        <strong>Raw Token Info:</strong>
        <pre className="mt-1 overflow-x-auto">
          {JSON.stringify(
            {
              hasAccessToken: !!session.accessToken,
              hasRefreshToken: !!session.refreshToken,
              sessionExpires: session.expires,
              tokenInfo,
              isRefreshing,
              refreshError,
              retryCount,
            },
            null,
            2
          )}
        </pre>
      </div>
    </div>
  );
}

// Component to manually trigger token refresh for testing
export function TokenRefreshTester() {
  const { checkAndRefreshToken, isRefreshing } = useTokenRefresh();
  const [lastRefresh, setLastRefresh] = useState<string | null>(null);

  const handleManualRefresh = async () => {
    try {
      await checkAndRefreshToken();
      setLastRefresh(new Date().toLocaleTimeString());
    } catch (error) {
      console.error("Manual refresh failed:", error);
    }
  };

  return (
    <div className="p-4 bg-blue-50 rounded-lg">
      <h3 className="font-bold text-blue-800 mb-2">Token Refresh Tester</h3>

      <button
        onClick={handleManualRefresh}
        disabled={isRefreshing}
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
      >
        {isRefreshing ? "Refreshing..." : "Manual Refresh"}
      </button>

      {lastRefresh && (
        <p className="mt-2 text-sm text-blue-600">
          Last manual refresh: {lastRefresh}
        </p>
      )}
    </div>
  );
}
