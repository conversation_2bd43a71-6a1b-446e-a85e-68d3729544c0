"use client";

import { DirectionProvider } from "@radix-ui/react-direction";
import { SessionProvider } from "next-auth/react";
import { UserProvider } from "@/contexts/user-context";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider
      basePath="/api/auth"
      refetchInterval={5 * 60} // Refetch session every 5 minutes
      refetchOnWindowFocus={true}
      refetchWhenOffline={false}
    >
      <UserProvider>
        <DirectionProvider dir="rtl">{children}</DirectionProvider>
      </UserProvider>
    </SessionProvider>
  );
}
