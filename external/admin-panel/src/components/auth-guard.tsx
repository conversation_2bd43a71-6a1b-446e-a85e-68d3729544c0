"use client";

import Loading from "@/app/admin/dashboard/loading";
import { useSession } from "next-auth/react";
import { useRouter, usePathname } from "next/navigation";
import { useEffect, ReactNode } from "react";
import { UserRole, hasAnyRole } from "@/lib/auth-types";
import { useTranslations } from "next-intl";
import { useTokenRefresh } from "@/hooks/use-token-refresh";

interface AuthGuardProps {
  children: ReactNode;
  requiredRoles?: UserRole[];
  fallback?: ReactNode;
  redirectTo?: string;
}

export default function AuthGuard({
  children,
  requiredRoles,
  fallback,
  redirectTo = "/login",
}: AuthGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();
  const t = useTranslations("auth.login");

  // Use the token refresh hook
  const { isRefreshing } = useTokenRefresh({
    onRefreshError: (error) => {
      console.error("❌ AuthGuard - Token refresh error:", error);
      // The hook handles logout/redirect automatically for critical errors
    },
  });

  // Debug logging
  console.log("AuthGuard - Render:", {
    pathname,
    status,
    hasSession: !!session,
    sessionKeys: session ? Object.keys(session) : null,
    isRefreshing,
  });

  useEffect(() => {
    console.log("AuthGuard effect:", { status, session: !!session, pathname });

    if (status === "loading" || isRefreshing) return; // Still loading or refreshing

    if (!session) {
      // User is not authenticated
      console.log("No session found, redirecting to login");
      const loginUrl = new URL(redirectTo, window.location.origin);
      loginUrl.searchParams.set("returnUrl", pathname);
      router.push(loginUrl.toString());
      return;
    }

    console.log("Session found, user authenticated");

    // Get user roles from session
    const userRoles = session.user?.roles || [];
    console.log("User roles:", userRoles);

    // Check role requirements
    const enableRoleCheck =
      process.env.NEXT_PUBLIC_ENABLE_ADMIN_ROLE_CHECK === "true";

    if (enableRoleCheck) {
      let hasRequiredAccess = true;
      let redirectReason = "";

      // Check specific roles requirement
      if (requiredRoles && !hasAnyRole(userRoles, requiredRoles)) {
        hasRequiredAccess = false;
        redirectReason = "insufficient_roles | " + requiredRoles.join(", ");
      }

      if (!hasRequiredAccess) {
        console.log("Access denied - insufficient roles:", {
          userRoles,
          requiredRoles,
          redirectReason,
        });
        const forbiddenUrl = new URL("/403", window.location.origin);
        forbiddenUrl.searchParams.set("reason", redirectReason);
        router.push(forbiddenUrl.toString());
        return;
      }
    }

    // Check for NextAuth session expiration (fallback)
    if (session.expires && new Date(session.expires) < new Date()) {
      console.log("⚠️ AuthGuard - NextAuth session expired");
      const loginUrl = new URL(redirectTo, window.location.origin);
      loginUrl.searchParams.set("returnUrl", pathname);
      loginUrl.searchParams.set("expired", "true");
      router.push(loginUrl.toString());
      return;
    }
  }, [
    session,
    status,
    router,
    pathname,
    requiredRoles,
    redirectTo,
    isRefreshing,
  ]);

  // Show loading state
  if (status === "loading" || isRefreshing) {
    return <Loading />;
  }

  // Show access denied for unauthenticated users
  if (!session) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {t("accessDenied")}
            </h1>
            <p className="text-gray-600">{t("pleaseLogIn")}</p>
          </div>
        </div>
      )
    );
  }

  // Check for expired session
  if (session.expires && new Date(session.expires) < new Date()) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {t("sessionExpired")}
            </h1>
            <p className="text-gray-600">{t("pleaseLogIn")}</p>
          </div>
        </div>
      )
    );
  }

  return <>{children}</>;
}

// Higher-order component for protecting pages
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<AuthGuardProps, "children"> = {}
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <AuthGuard {...options}>
        <Component {...props} />
      </AuthGuard>
    );
  };
}

// Convenience HOCs for specific roles
export function withAdminGuard<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<AuthGuardProps, "children" | "requiredRoles"> = {}
) {
  return withAuthGuard(Component, { ...options, requiredRoles: ["ADMIN"] });
}

export function withAffiliateGuard<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<AuthGuardProps, "children" | "requiredRoles"> = {}
) {
  return withAuthGuard(Component, { ...options, requiredRoles: ["AFFILIATE"] });
}
