"use client";

import * as React from "react";
import * as LabelPrimitive from "@radix-ui/react-label";

import { cn } from "@/lib/utils";

function Label({
  className,
  isRequired = false,
  ...props
}: React.ComponentProps<typeof LabelPrimitive.Root> & {
  isRequired?: boolean;
}) {
  return (
    <div className="flex flex-row gap-1.5 py-0 items-baseline justify-start w-full">
      <LabelPrimitive.Root
        data-slot="label"
        className={cn(
          "h-6 flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",
          className
        )}
        {...props}
      />
      {isRequired && <span className="text-red-500 text-sm">*</span>}
    </div>
  );
}

export { Label };
