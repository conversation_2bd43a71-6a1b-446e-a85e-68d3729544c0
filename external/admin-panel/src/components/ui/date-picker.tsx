import React from "react";
import DatePicker, {
  CalendarProps,
  DatePickerProps,
} from "react-multi-date-picker";
import persian from "react-date-object/calendars/persian";
import gregorian from "react-date-object/calendars/gregorian";
import gregorian_en from "react-date-object/locales/gregorian_en";
import persian_fa from "react-date-object/locales/persian_fa";
import { cn } from "@/lib/utils";

interface CustomDatePickerProps {
  calendarType?: "persian" | "gregorian";
  language?: "fa" | "en";
  error?: boolean;
}

const CustomDatePicker = <
  Multiple extends boolean = false,
  Range extends boolean = false
>(
  props: Omit<CalendarProps<Multiple, Range>, "locale" | "calendar"> &
    DatePickerProps &
    CustomDatePickerProps
) => {
  const calendar = props.calendarType === "gregorian" ? gregorian : persian;
  const locale = props.language === "en" ? gregorian_en : persian_fa;

  const inputClass = cn(
    "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
    "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
    "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
    props.error && "border-red-500",
    props.className
  );

  return (
    <DatePicker
      containerStyle={{ width: "100%" }}
      value={props.value}
      onChange={props.onChange}
      minDate={props.minDate}
      maxDate={props.maxDate}
      calendar={calendar}
      format="YYYY-MM-DD"
      locale={locale}
      onOpenPickNewDate={props.onOpenPickNewDate ?? false}
      inputClass={inputClass}
      placeholder={props.placeholder}
      disabled={props.disabled}
      id={props.id}
      {...props}
    />
  );
};

export default CustomDatePicker;
