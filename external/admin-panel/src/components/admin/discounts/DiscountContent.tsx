import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { DiscountOutputDto } from "@/lib/admin/discount-types";
import { useTranslations } from "next-intl";
import { DiscountTable } from "./DiscountTable";
import { DiscountMobileCards } from "./DiscountMobileCards";

interface DiscountContentProps {
  loading: boolean;
  error: string;
  filteredDiscounts: DiscountOutputDto[];
  onRefresh: () => void;
  onCreateNew: () => void;
}

export function DiscountContent({
  loading,
  error,
  filteredDiscounts,
  onRefresh,
  onCreateNew,
}: DiscountContentProps) {
  const t = useTranslations("admin.discount.discounts");

  if (loading) {
    return (
      <Card>
        <CardContent className="p-0">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">{t("messages.loading")}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-0">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="text-red-600 mb-2">⚠️</div>
              <p className="text-red-600 font-medium mb-2">{error}</p>
              <Button onClick={onRefresh} variant="outline" size="sm">
                {t("actions.refresh")}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (filteredDiscounts.length === 0) {
    return (
      <Card>
        <CardContent className="p-0">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="text-gray-400 mb-2">📋</div>
              <p className="text-gray-600 font-medium mb-1">
                {t("table.noData")}
              </p>
              <p className="text-gray-500 text-sm mb-4">
                {t("table.noDataDescription")}
              </p>
              <Button onClick={onCreateNew} size="sm">
                {t("actions.createNew")}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-0">
        <DiscountTable discounts={filteredDiscounts} />
        <DiscountMobileCards discounts={filteredDiscounts} />
      </CardContent>
    </Card>
  );
}
