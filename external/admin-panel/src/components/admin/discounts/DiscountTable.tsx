import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDate } from "@/lib/date-utils";
import { DiscountOutputDto } from "@/lib/admin/discount-types";
import { isExpired } from "@/lib/admin/discount-utils";
import { useTranslations } from "next-intl";

interface DiscountTableProps {
  discounts: DiscountOutputDto[];
}

export function DiscountTable({ discounts }: DiscountTableProps) {
  const t = useTranslations("admin.discount.discounts");

  return (
    <div className="hidden md:block overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t("table.headers.code")}</TableHead>
            <TableHead>{t("table.headers.percentage")}</TableHead>
            <TableHead>{t("table.headers.expiry")}</TableHead>
            <TableHead>{t("table.headers.status")}</TableHead>
            <TableHead>{t("table.headers.type")}</TableHead>
            <TableHead>{t("table.headers.description")}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {discounts.map((discount) => (
            <TableRow key={discount.id}>
              <TableCell className="font-medium">{discount.code}</TableCell>
              <TableCell>{discount.discount_percent}%</TableCell>
              <TableCell>
                <div
                  className={
                    isExpired(discount.expire_date) ? "text-red-600" : ""
                  }
                >
                  {formatDate(discount.expire_date)}
                </div>
              </TableCell>
              <TableCell>
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    discount.state === "ACTIVE"
                      ? "bg-green-100 text-green-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {t(`table.status.${discount.state}`)}
                </span>
              </TableCell>
              <TableCell>{t(`table.type.${discount.type}`)}</TableCell>
              <TableCell className="max-w-xs truncate">
                {discount.description || "-"}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
