import { Card } from "@/components/ui/card";
import { formatDate } from "@/lib/date-utils";
import { DiscountOutputDto } from "@/lib/admin/discount-types";
import { isExpired } from "@/lib/admin/discount-utils";
import { useTranslations } from "next-intl";

interface DiscountMobileCardsProps {
  discounts: DiscountOutputDto[];
}

export function DiscountMobileCards({ discounts }: DiscountMobileCardsProps) {
  const t = useTranslations("admin.discount.discounts");

  return (
    <div className="md:hidden space-y-4 p-4">
      {discounts.map((discount) => (
        <Card key={discount.id} className="p-4">
          <div className="space-y-3">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-medium text-lg">{discount.code}</h3>
                <p className="text-sm text-gray-600">
                  {t("table.headers.percentage")} : &nbsp;
                  {discount.discount_percent}%
                </p>
              </div>
              <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  discount.state === "ACTIVE"
                    ? "bg-green-100 text-green-800"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                {t(`table.status.${discount.state}`)}
              </span>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">
                  {t("table.headers.expiry")}:
                </span>
                <div
                  className={
                    isExpired(discount.expire_date) ? "text-red-600" : ""
                  }
                >
                  {formatDate(discount.expire_date)}
                </div>
              </div>
              <div>
                <span className="text-gray-500">
                  {t("table.headers.type")}:
                </span>
                <div>{t(`table.type.${discount.type}`)}</div>
              </div>
              {discount.description && (
                <div className="col-span-2">
                  <span className="text-gray-500">
                    {t("table.headers.description")}:
                  </span>
                  <div className="mt-1">{discount.description}</div>
                </div>
              )}
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}
