import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import CustomDatePicker from "@/components/ui/date-picker";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { DiscountQueryParams } from "@/lib/admin/discount-types";
import type { DiscountFilters } from "@/lib/admin/discount-types";
import { exportDiscountsToExcel } from "@/lib/admin/discount-api";
import { DateObject } from "react-multi-date-picker";
import gregorian from "react-date-object/calendars/gregorian";
import gregorian_en from "react-date-object/locales/gregorian_en";

interface DiscountFiltersProps {
  onRefresh: () => void;
  loading: boolean;
  onFiltersChange?: (filters: Partial<DiscountQueryParams>) => void;
  initialFilters?: Partial<DiscountQueryParams>;
}

export function DiscountFilters({
  onRefresh,
  loading,
  onFiltersChange,
  initialFilters = {},
}: DiscountFiltersProps) {
  const t = useTranslations("admin.discount.discounts");
  const router = useRouter();
  const [exportLoading, setExportLoading] = useState(false);

  const [filters, setFilters] = useState<Partial<DiscountQueryParams>>({
    state: initialFilters.state,
    type: initialFilters.type,
    code: initialFilters.code || "",
    description: initialFilters.description || "",
    created_date_from: initialFilters.created_date_from || "",
    created_date_to: initialFilters.created_date_to || "",
    expire_date_from: initialFilters.expire_date_from || "",
    expire_date_to: initialFilters.expire_date_to || "",
  });

  // Update state when initialFilters change
  useEffect(() => {
    setFilters({
      state: initialFilters.state,
      type: initialFilters.type,
      code: initialFilters.code || "",
      description: initialFilters.description || "",
      created_date_from: initialFilters.created_date_from || "",
      created_date_to: initialFilters.created_date_to || "",
      expire_date_from: initialFilters.expire_date_from || "",
      expire_date_to: initialFilters.expire_date_to || "",
    });
  }, [initialFilters]);

  const handleCreateNew = () => {
    router.push("/admin/dashboard/generate-discount");
  };

  const handleFilterChange = (
    key: keyof DiscountQueryParams,
    value: string | number | undefined
  ) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const handleRefresh = () => {
    onRefresh();
  };

  const handleClearFilters = () => {
    const clearedFilters = {
      state: undefined,
      type: undefined,
      code: "",
      description: "",
      created_date_from: "",
      created_date_to: "",
      expire_date_from: "",
      expire_date_to: "",
    };
    setFilters(clearedFilters);
    onFiltersChange?.(clearedFilters);
    onRefresh();
  };

  const handleExport = async () => {
    setExportLoading(true);

    try {
      // Prepare filters for export (exclude pagination)
      const exportFilters: DiscountFilters = {
        state: filters.state,
        type: filters.type,
        code: filters.code || undefined,
        description: filters.description || undefined,
        created_date_from: filters.created_date_from || undefined,
        created_date_to: filters.created_date_to || undefined,
        expire_date_from: filters.expire_date_from || undefined,
        expire_date_to: filters.expire_date_to || undefined,
      };

      const result = await exportDiscountsToExcel(exportFilters);

      if (result.success && result.blob) {
        // Create download link
        const url = window.URL.createObjectURL(result.blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `discounts_${
          new Date().toISOString().split("T")[0]
        }.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        alert(t("messages.exportSuccess"));
      } else {
        alert(result.message || t("messages.exportError"));
      }
    } catch (error) {
      console.error("Export error:", error);
      alert(t("messages.exportError"));
    } finally {
      setExportLoading(false);
    }
  };

  return (
    <div className="bg-white p-4 rounded-lg border mb-6">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">{t("filters.title")}</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-2">
            <Button
              onClick={handleClearFilters}
              variant="outline"
              disabled={loading}
            >
              {t("actions.clearFilters")}
            </Button>
            <Button
              onClick={handleRefresh}
              variant="outline"
              disabled={loading}
            >
              {t("actions.refresh")}
            </Button>
            <Button
              onClick={handleExport}
              variant="outline"
              disabled={loading || exportLoading}
            >
              {exportLoading
                ? t("messages.exportLoading")
                : t("actions.exportToExcel")}
            </Button>
            <Button onClick={handleCreateNew}>{t("actions.createNew")}</Button>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4  gap-4">
          <div className="space-y-2">
            <Label htmlFor="state-filter">{t("filters.state")}</Label>
            <Select
              value={filters.state || ""}
              onValueChange={(value) =>
                handleFilterChange("state", value === "all" ? undefined : value)
              }
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t("filters.allStates")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("filters.allStates")}</SelectItem>
                <SelectItem value="ACTIVE">{t("filters.active")}</SelectItem>
                <SelectItem value="DEACTIVE">
                  {t("filters.deactive")}
                </SelectItem>
                <SelectItem value="USED">{t("filters.used")}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="type-filter">{t("filters.type")}</Label>
            <Select
              value={filters.type || ""}
              onValueChange={(value) =>
                handleFilterChange("type", value === "all" ? undefined : value)
              }
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t("filters.allTypes")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("filters.allTypes")}</SelectItem>
                <SelectItem value="simple">{t("filters.simple")}</SelectItem>
                <SelectItem value="referral">
                  {t("filters.referral")}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="code-filter">{t("filters.code")}</Label>
            <Input
              id="code-filter"
              placeholder={t("filters.codePlaceholder")}
              value={filters.code || ""}
              onChange={(e) => handleFilterChange("code", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description-filter">
              {t("filters.description")}
            </Label>
            <Input
              id="description-filter"
              placeholder={t("filters.descriptionPlaceholder")}
              value={filters.description || ""}
              onChange={(e) =>
                handleFilterChange("description", e.target.value)
              }
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="created-date-from-filter">
              {t("filters.createdDateFrom")}
            </Label>
            <CustomDatePicker
              id="created-date-from-filter"
              placeholder={t("filters.createdDateFromPlaceholder")}
              value={
                filters.created_date_from
                  ? new DateObject(filters.created_date_from)
                  : null
              }
              onChange={(date: DateObject | null) => {
                const dateString = date
                  ? date.convert(gregorian, gregorian_en).format("YYYY-MM-DD")
                  : "";
                handleFilterChange("created_date_from", dateString);
              }}
              calendarType="persian"
              language="fa"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="created-date-to-filter">
              {t("filters.createdDateTo")}
            </Label>
            <CustomDatePicker
              id="created-date-to-filter"
              placeholder={t("filters.createdDateToPlaceholder")}
              value={
                filters.created_date_to
                  ? new DateObject(filters.created_date_to)
                  : null
              }
              onChange={(date: DateObject | null) => {
                const dateString = date
                  ? date.convert(gregorian, gregorian_en).format("YYYY-MM-DD")
                  : "";
                handleFilterChange("created_date_to", dateString);
              }}
              calendarType="persian"
              language="fa"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="expire-date-from-filter">
              {t("filters.expireDateFrom")}
            </Label>
            <CustomDatePicker
              id="expire-date-from-filter"
              placeholder={t("filters.expireDateFromPlaceholder")}
              value={
                filters.expire_date_from
                  ? new DateObject(filters.expire_date_from)
                  : null
              }
              onChange={(date: DateObject | null) => {
                const dateString = date
                  ? date.convert(gregorian, gregorian_en).format("YYYY-MM-DD")
                  : "";
                handleFilterChange("expire_date_from", dateString);
              }}
              calendarType="persian"
              language="fa"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="expire-date-to-filter">
              {t("filters.expireDateTo")}
            </Label>
            <CustomDatePicker
              id="expire-date-to-filter"
              placeholder={t("filters.expireDateToPlaceholder")}
              value={
                filters.expire_date_to
                  ? new DateObject(filters.expire_date_to)
                  : null
              }
              onChange={(date: DateObject | null) => {
                const dateString = date
                  ? date.convert(gregorian, gregorian_en).format("YYYY-MM-DD")
                  : "";
                handleFilterChange("expire_date_to", dateString);
              }}
              calendarType="persian"
              language="fa"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
