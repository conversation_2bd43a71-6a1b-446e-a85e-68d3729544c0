"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import {
  UserRole,
  UserProfileDto,
  hasRole,
  hasAnyRole,
  isAdmin,
  isAffiliate,
} from "@/lib/auth-types";

interface UserContextType {
  // User data
  user: UserProfileDto | null;
  roles: UserRole[];
  isLoading: boolean;
  isAuthenticated: boolean;

  // Role checking utilities
  hasRole: (role: UserRole) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;
  isAdmin: () => boolean;
  isAffiliate: () => boolean;
  canAccessAdminPanel: () => boolean;
  canAccessAffiliatePanel: () => boolean;

  // Session data
  accessToken: string | null;
  refreshToken: string | null;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

interface UserProviderProps {
  children: React.ReactNode;
}

export function UserProvider({ children }: UserProviderProps) {
  const { data: session, status } = useSession();
  const [user, setUser] = useState<UserProfileDto | null>(null);
  const [roles, setRoles] = useState<UserRole[]>([]);

  useEffect(() => {
    if (session?.user) {
      setUser(session.user.profile || null);
      setRoles(session.user.roles || []);
    } else {
      setUser(null);
      setRoles([]);
    }
  }, [session]);

  const contextValue: UserContextType = {
    // User data
    user,
    roles,
    isLoading: status === "loading",
    isAuthenticated: !!session,

    // Role checking utilities
    hasRole: (role: UserRole) => hasRole(roles, role),
    hasAnyRole: (requiredRoles: UserRole[]) => hasAnyRole(roles, requiredRoles),
    isAdmin: () => isAdmin(roles),
    isAffiliate: () => isAffiliate(roles),
    canAccessAdminPanel: () => hasAnyRole(roles, ["ADMIN"]),
    canAccessAffiliatePanel: () => hasAnyRole(roles, ["AFFILIATE"]),

    // Session data
    accessToken: session?.accessToken || null,
    refreshToken: session?.refreshToken || null,
  };

  return (
    <UserContext.Provider value={contextValue}>{children}</UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
}
// Role-based component wrapper
interface RoleGuardProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
  requireAdmin?: boolean;
  requireAffiliate?: boolean;
  fallback?: React.ReactNode;
}

export function RoleGuard({
  children,
  requiredRoles,
  requireAdmin,
  requireAffiliate,
  fallback = null,
}: RoleGuardProps) {
  const { hasRole, hasAnyRole, isLoading } = useUser();

  if (isLoading) {
    return fallback;
  }

  let hasAccess = true;

  if (requireAdmin && !hasRole("ADMIN")) {
    hasAccess = false;
  }

  if (requireAffiliate && !hasRole("AFFILIATE")) {
    hasAccess = false;
  }

  if (requiredRoles && !hasAnyRole(requiredRoles)) {
    hasAccess = false;
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}

// Component-level role checking utilities
export function AdminOnly({
  children,
  fallback = null,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <RoleGuard requireAdmin fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function AffiliateOnly({
  children,
  fallback = null,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  return (
    <RoleGuard requireAffiliate fallback={fallback}>
      {children}
    </RoleGuard>
  );
}
