import {
  format as formatGre<PERSON>ian,
  parseISO,
  isValid,
  formatDistanceToNow,
} from "date-fns";
import {
  format as format<PERSON><PERSON><PERSON>,
  parseISO as parseISOJalali,
  isValid as isValid<PERSON>alali,
  formatDistanceToNow as formatDistanceToNowJalali,
} from "date-fns-jalali";

export type DateFormat = "gregorian" | "jalali" | "auto";

/**
 * Centralized date formatting utility that supports both Gregorian and Jalali calendars
 * @param date - Date string or Date object
 * @param formatStr - Format string (same as date-fns format)
 * @param calendar - Calendar type: 'gregorian', 'jalali', or 'auto' (defaults to jalali for Persian locale)
 * @returns Formatted date string
 */
export function formatDate(
  date: string | Date | number,
  formatStr: string = "yyyy/MM/dd",
  calendar: DateFormat = "jalali"
): string {
  let dateObj: Date;

  // Parse the input date
  if (typeof date === "string") {
    dateObj = parseISO(date);
    if (!isValid(dateObj)) {
      // Try Jalali parsing if <PERSON><PERSON> fails
      try {
        dateObj = parseISOJalali(date);
      } catch {
        return "تاریخ نامعتبر";
      }
    }
  } else if (typeof date === "number") {
    dateObj = new Date(date);
  } else {
    dateObj = date;
  }

  if (!isValid(dateObj) && !isValidJalali(dateObj)) {
    return "تاریخ نامعتبر";
  }

  // Determine calendar based on preference
  const useJalali =
    calendar === "jalali" || (calendar === "auto" && shouldUseJalali());

  try {
    if (useJalali) {
      return formatJalali(dateObj, formatStr);
    } else {
      return formatGregorian(dateObj, formatStr);
    }
  } catch (error) {
    // Fallback to Gregorian if Jalali fails
    try {
      return formatGregorian(dateObj, formatStr);
    } catch {
      return "خطا در قالب‌بندی تاریخ";
    }
  }
}

/**
 * Format relative time (e.g., "2 hours ago")
 * @param date - Date to format
 * @param calendar - Calendar type
 * @returns Relative time string
 */
export function formatRelativeTime(
  date: string | Date | number,
  calendar: DateFormat = "jalali"
): string {
  let dateObj: Date;

  if (typeof date === "string") {
    dateObj = parseISO(date);
  } else if (typeof date === "number") {
    dateObj = new Date(date);
  } else {
    dateObj = date;
  }

  if (!isValid(dateObj)) {
    return "تاریخ نامعتبر";
  }

  const useJalali =
    calendar === "jalali" || (calendar === "auto" && shouldUseJalali());

  try {
    if (useJalali) {
      return formatDistanceToNowJalali(dateObj, { addSuffix: true });
    } else {
      return formatDistanceToNow(dateObj, { addSuffix: true });
    }
  } catch {
    return formatDistanceToNow(dateObj, { addSuffix: true });
  }
}

/**
 * Check if Jalali calendar should be used based on locale
 * @returns true if Jalali should be used
 */
function shouldUseJalali(): boolean {
  // Check if we're in a Persian locale context
  if (typeof window !== "undefined") {
    const locale = localStorage.getItem("i18nextLng") || "fa";
    return locale.startsWith("fa");
  }
  return true; // Default to Jalali for Persian context
}

/**
 * Convert between Gregorian and Jalali dates
 * @param date - Date to convert
 * @param from - Source calendar
 * @param to - Target calendar
 * @returns Converted date
 */
export function convertDate(
  date: string | Date | number,
  from: "gregorian" | "jalali",
  to: "gregorian" | "jalali"
): Date {
  // This is a simplified implementation
  // In a real-world scenario, you might want to use more sophisticated conversion
  let dateObj: Date;

  if (typeof date === "string") {
    if (from === "jalali") {
      dateObj = parseISOJalali(date);
    } else {
      dateObj = parseISO(date);
    }
  } else if (typeof date === "number") {
    dateObj = new Date(date);
  } else {
    dateObj = date;
  }

  return dateObj;
}
