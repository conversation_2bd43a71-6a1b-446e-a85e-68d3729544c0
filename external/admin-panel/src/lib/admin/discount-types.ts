// Discount DTOs based on Swagger API documentation

import { DateObject } from "react-multi-date-picker";

export interface DiscountInputDto {
  expire_date: string; // YYYY-MM-DD format
  prefix: string | null;
  discount_percent: number;
  count: number;
  type: "simple" | "referral";
  referral_id: number;
  description?: string;
}

export interface DiscountOutputDto {
  id: number;
  code: string;
  discount_percent: number;
  expire_date: string; // ISO date-time format
  state: "ACTIVE" | "DEACTIVE" | "USED";
  type: "simple" | "referral";
  referral_id: number;
  description: string;
}

export interface CreateDiscountResponse {
  success: boolean;
  message: string;
  data?: DiscountOutputDto[];
}

export interface DiscountFormData {
  expireDate: DateObject | null;
  prefix: string | null;
  discountPercent: number;
  count: number;
  type: "simple" | "referral";
  referralId: number;
  description?: string;
}

// Form validation types
export interface DiscountFormErrors {
  expireDate?: string;
  discountPercent?: string;
  count?: string;
  type?: string;
  referralId?: string;
  description?: string;
}

// API response types
export interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
}

// Pagination response type
export interface DiscountPaginationOutputDto {
  data: DiscountOutputDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  activeCount: number;
  usedCount: number;
  expiredCount: number;
}

// Filter parameters for discount API
export interface DiscountFilters {
  state?: "ACTIVE" | "DEACTIVE" | "USED";
  type?: "simple" | "referral";
  referral_id?: number;
  description?: string;
  code?: string;
  created_date_from?: string; // YYYY-MM-DD
  created_date_to?: string; // YYYY-MM-DD
  expire_date_from?: string; // YYYY-MM-DD
  expire_date_to?: string; // YYYY-MM-DD
}

export interface DiscountStatsParams {
  totalCount: number;
  activeCount: number;
  usedCount: number;
  expiredCount: number;
}

// Pagination parameters
export interface PaginationParams {
  page?: number;
  limit?: number;
}

// Combined filter and pagination parameters
export interface DiscountQueryParams
  extends DiscountFilters,
    PaginationParams {}
