import { api, AppApiError } from "../api-client";
import {
  DiscountInputDto,
  DiscountOutputDto,
  CreateDiscountResponse,
  DiscountPaginationOutputDto,
  DiscountQueryParams,
  DiscountFilters,
} from "./discount-types";

/**
 * Create a new discount code
 * @param discountData - The discount data to create
 * @returns Promise with the created discount or error
 */
export async function createDiscount(
  discountData: DiscountInputDto
): Promise<CreateDiscountResponse> {
  try {
    console.log("Creating discount with data:", discountData);

    const response = await api.postJson<DiscountOutputDto[]>(
      `/discounts/create`,
      discountData
    );

    return {
      success: true,
      message: "کد تخفیف با موفقیت ایجاد شد",
      data: response,
    };
  } catch (error) {
    console.error("Create discount error:", error);

    let errorMessage = "خطا در ایجاد کد تخفیف";

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        errorMessage = "درخواست منقضی شد - لطفاً دوباره تلاش کنید";
      } else if (error.message.includes("fetch")) {
        errorMessage = "خطا در اتصال به سرور";
      } else if (error instanceof AppApiError) {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Export discount codes to Excel
 * @param filters - Filter parameters for the export
 * @returns Promise with the Excel blob or error
 */
export async function exportDiscountsToExcel(
  filters: DiscountFilters = {}
): Promise<{
  success: boolean;
  blob?: Blob;
  message?: string;
}> {
  try {
    const url = new URL(`/discounts/all/excel`);

    // Add filter parameters to URL
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        url.searchParams.append(key, value.toString());
      }
    });

    console.log("Exporting discounts with filters:", filters);

    const response = await api.get(url.toString());

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Export discounts failed:", response.status, errorText);

      let errorMessage = "خطا در خروجی گرفتن از کدهای تخفیف";

      try {
        const errorData = JSON.parse(errorText).message;
        errorMessage = errorData || errorMessage;
      } catch {
        // If parsing fails, use default message
      }

      return {
        success: false,
        message: errorMessage,
      };
    }

    const blob = await response.blob();
    console.log("Discounts exported successfully, blob size:", blob.size);

    return {
      success: true,
      blob,
    };
  } catch (error) {
    console.error("Export discounts error:", error);

    let errorMessage = "خطا در خروجی گرفتن از کدهای تخفیف";

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        errorMessage = "درخواست منقضی شد - لطفاً دوباره تلاش کنید";
      } else if (error.message.includes("fetch")) {
        errorMessage = "خطا در اتصال به سرور";
      }
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Get all discount codes
 * @param accessToken - The user's access token for authentication
 * @param state - Optional filter by state
 * @returns Promise with the discount list or error
 */
/**
 * Get all discount codes with filtering and pagination
 * @param params - Query parameters for filtering and pagination
 * @returns Promise with the paginated discount list or error
 */
export async function getDiscounts(params: DiscountQueryParams = {}): Promise<{
  success: boolean;
  data?: DiscountPaginationOutputDto;
  message?: string;
}> {
  try {
    let url = `/discounts/all`;

    // Add query parameters
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        searchParams.append(key, value.toString());
      }
    });
    url += `?${searchParams.toString()}`;

    const response = await api.getJson<DiscountPaginationOutputDto>(
      url.toString()
    );

    return {
      success: true,
      data: response,
    };
  } catch (error) {
    let errorMessage = "خطا در دریافت لیست کدهای تخفیف";

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        errorMessage = "درخواست منقضی شد - لطفاً دوباره تلاش کنید";
      } else if (error.message.includes("fetch")) {
        errorMessage = "خطا در اتصال به سرور";
      }
    } else if (error instanceof AppApiError) {
      errorMessage = error.message;
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}
