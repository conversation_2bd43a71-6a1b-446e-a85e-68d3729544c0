const persianToEnglishMap: { [key: string]: string } = {
  // Persian digits (Unicode: \u06F0 - \u06F9)
  "۰": "0",
  "۱": "1",
  "۲": "2",
  "۳": "3",
  "۴": "4",
  "۵": "5",
  "۶": "6",
  "۷": "7",
  "۸": "8",
  "۹": "9",
  // Arabic digits (Unicode: \u0660 - \u0669)
  // Often used interchangeably in some contexts
  "٠": "0",
  "١": "1",
  "٢": "2",
  "٣": "3",
  "٤": "4",
  "٥": "5",
  "٦": "6",
  "٧": "7",
  "٨": "8",
  "٩": "9",
  // Arabic decimal separator (comma) - sometimes used instead of slash
  "٬": ",",
  // Persian decimal separator (slash) - convert to standard decimal point
  "／": ".",
};

// Convert Persian digits and decimal separator to English
export function toEnglishDigits(str: string): string {
  return str.replace(
    /[۰-۹۰-۹٬／]/g,
    (matchedChar) => persianToEnglishMap[matchedChar] || matchedChar
  );
}

// Convert English digits and decimal separator to Persian
export function toPersianDigits(str: string): string {
  return str.replace(
    /[0-9.,]/g,
    (matchedChar) => persianToEnglishMap[matchedChar] || matchedChar
  );
}
