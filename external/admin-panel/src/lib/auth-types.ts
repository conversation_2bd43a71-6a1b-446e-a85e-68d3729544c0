// Authentication DTOs based on Swagger API documentation

export interface CheckRegisterPhoneInputDto {
  country_name: string;
  country_code: string;
  phone: string;
}

export interface CheckPhoneRegisteredOutputDto {
  status: "SendEmail" | "SendSms" | "GetEmail";
  email: string;
}

export interface UserLoginInputDto {
  country_code: string;
  phone: string;
  confirmation_code: string;
  device_id: string;
}

export interface UserLoginOutputDto {
  access_token: string;
  refresh_token: string;
}

export interface AuthError {
  message: string;
  status?: number;
}

// User roles enum based on backend API
export type UserRole = "ADMIN" | "AFFILIATE" | "USER";

// User profile data from backend API
export interface UserProfileDto {
  id: number;
  name: string;
  family: string;
  country_code: string;
  country_name: string;
  phone: string;
  email: string;
  birth_date: string;
  gender: string;
  userType: string;
  verified_by: string;
  status: "PENDING" | "AWAITING_CONFIRMATION" | "ACTIVE" | "DISABLED";
  roles: UserRole[];
  created_at: string;
}

// Extended user data for NextAuth
export interface ExtendedUser {
  id: string;
  phone: string;
  accessToken: string;
  refreshToken: string;
  profile?: UserProfileDto;
  roles?: UserRole[];
}

// Constants
export const IRAN_COUNTRY_CODE = "+98";
export const IRAN_COUNTRY_NAME = "IR";

// Role checking utilities
export const hasRole = (
  userRoles: UserRole[] | undefined,
  requiredRole: UserRole
): boolean => {
  return userRoles?.includes(requiredRole) ?? false;
};

export const hasAnyRole = (
  userRoles: UserRole[] | undefined,
  requiredRoles: UserRole[]
): boolean => {
  return requiredRoles.some((role) => hasRole(userRoles, role));
};

export const isAdmin = (userRoles: UserRole[] | undefined): boolean => {
  return hasRole(userRoles, "ADMIN");
};

export const isAffiliate = (userRoles: UserRole[] | undefined): boolean => {
  return hasRole(userRoles, "AFFILIATE");
};

export const canAccessAdminPanel = (
  userRoles: UserRole[] | undefined
): boolean => {
  return hasAnyRole(userRoles, ["ADMIN"]);
};

export const canAccessAffiliatePanel = (
  userRoles: UserRole[] | undefined
): boolean => {
  return hasAnyRole(userRoles, ["AFFILIATE"]);
};

// Token refresh DTO
export interface RefreshTokenDto {
  refresh_token: string;
}

// Token expiration utilities
export const isTokenExpired = (token: string): boolean => {
  try {
    // Decode JWT without verification to check expiration
    const base64Url = token.split(".")[1];
    const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split("")
        .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
        .join("")
    );

    const payload = JSON.parse(jsonPayload);
    const currentTime = Math.floor(Date.now() / 1000);

    // Check if token has exp claim and if it's expired
    return payload.exp && payload.exp < currentTime;
  } catch (error) {
    console.error("Error checking token expiration:", error);
    // If we can't decode the token, consider it expired for safety
    return true;
  }
};

export const getTokenExpiration = (token: string): number | null => {
  try {
    const base64Url = token.split(".")[1];
    const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split("")
        .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
        .join("")
    );

    const payload = JSON.parse(jsonPayload);
    return payload.exp || null;
  } catch (error) {
    console.error("Error getting token expiration:", error);
    return null;
  }
};

export const isTokenExpiringSoon = (
  token: string,
  bufferMinutes: number = 5
): boolean => {
  try {
    const exp = getTokenExpiration(token);
    if (!exp) return true;

    const currentTime = Math.floor(Date.now() / 1000);
    const bufferSeconds = bufferMinutes * 60;

    // Check if token expires within the buffer time
    return exp < currentTime + bufferSeconds;
  } catch (error) {
    console.error("Error checking if token is expiring soon:", error);
    return true;
  }
};
