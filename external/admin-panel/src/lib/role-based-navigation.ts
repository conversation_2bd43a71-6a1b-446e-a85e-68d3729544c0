import { UserRole } from "./auth-types";

/**
 * Get the appropriate redirect URL based on user roles
 * Priority: ADMIN > AFFILIATE > 403 (if no valid roles)
 */
export function getRoleBasedRedirectUrl(roles: UserRole[]): string {
  const dashboardUrl = getUserDashboardUrl(roles);

  // If user has no recognized roles, redirect to 403 to avoid redirect loop
  return dashboardUrl || "/403?reason=insufficient_roles";
}

/**
 * Get the appropriate dashboard URL for a user based on their roles
 * Returns null if user has no valid roles (for conditional rendering)
 */
export function getUserDashboardUrl(roles: UserRole[]): string | null {
  if (canAccessAdmin(roles)) {
    return "/admin/dashboard";
  }

  if (canAccessAffiliate(roles)) {
    return "/affiliate/dashboard";
  }

  return null; // No valid dashboard for this user
}

/**
 * Check if a user can access admin features
 */
export function canAccessAdmin(roles: UserRole[]): boolean {
  return roles.includes("ADMIN");
}

/**
 * Check if a user can access affiliate features
 */
export function canAccessAffiliate(roles: UserRole[]): boolean {
  return roles.includes("AFFILIATE");
}

/**
 * Get the appropriate "home" URL for navigation purposes
 * This is used for navigation elements that need to link to the user's main dashboard
 */
export function getHomeUrl(roles: UserRole[]): string {
  const dashboardUrl = getUserDashboardUrl(roles);
  return dashboardUrl || "/login"; // Fallback to login if no dashboard available
}
