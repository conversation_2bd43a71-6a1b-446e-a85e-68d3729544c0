import { getSession } from "next-auth/react";
import { isTokenExpired, isTokenExpiringSoon } from "./auth-types";
import { refreshAccessToken } from "./token-refresh";

interface ApiClientOptions {
  baseURL?: string;
  timeout?: number;
  retries?: number;
}

class ApiClient {
  private baseURL: string;
  private timeout: number;
  private retries: number;
  private refreshPromise: Promise<string | null> | null = null;

  constructor(options: ApiClientOptions = {}) {
    this.baseURL = options.baseURL || process.env.NEXT_PUBLIC_API_BASE_URL || "";
    this.timeout = options.timeout || 10000;
    this.retries = options.retries || 3;
  }

  private async getValidAccessToken(): Promise<string | null> {
    try {
      const session = await getSession();
      
      if (!session?.accessToken) {
        console.warn("🔑 No access token in session");
        return null;
      }

      const accessToken = session.accessToken;

      // Check if token is expired or expiring soon
      if (isTokenExpired(accessToken) || isTokenExpiringSoon(accessToken, 2)) {
        console.log("🔄 API Client - Token expired/expiring, refreshing...");
        
        // Prevent multiple simultaneous refresh attempts
        if (!this.refreshPromise) {
          this.refreshPromise = this.refreshToken(session.refreshToken);
        }
        
        const newToken = await this.refreshPromise;
        this.refreshPromise = null;
        
        return newToken;
      }

      return accessToken;
    } catch (error) {
      console.error("🔑 Error getting valid access token:", error);
      return null;
    }
  }

  private async refreshToken(refreshToken: string | undefined): Promise<string | null> {
    if (!refreshToken) {
      console.error("🔄 No refresh token available");
      return null;
    }

    try {
      const result = await refreshAccessToken(refreshToken);
      
      if (result.success && result.accessToken) {
        console.log("✅ API Client - Token refresh successful");
        
        // Update the session with new tokens
        // Note: This is a simplified approach. In a real app, you might want to
        // trigger a session update through NextAuth
        return result.accessToken;
      } else {
        console.error("❌ API Client - Token refresh failed:", result.error);
        return null;
      }
    } catch (error) {
      console.error("❌ API Client - Token refresh error:", error);
      return null;
    }
  }

  private async makeRequest(
    url: string,
    options: RequestInit = {},
    attempt: number = 1
  ): Promise<Response> {
    try {
      // Get a valid access token
      const accessToken = await this.getValidAccessToken();
      
      // Prepare headers
      const headers: HeadersInit = {
        "Content-Type": "application/json",
        ...options.headers,
      };

      // Add authorization header if we have a token
      if (accessToken) {
        headers.Authorization = `Bearer ${accessToken}`;
      }

      // Make the request
      const response = await fetch(`${this.baseURL}${url}`, {
        ...options,
        headers,
        signal: AbortSignal.timeout(this.timeout),
      });

      // If we get a 401 and we have retries left, try to refresh and retry
      if (response.status === 401 && attempt <= this.retries) {
        console.log(`🔄 API Client - 401 response, retrying (attempt ${attempt}/${this.retries})`);
        
        // Clear any cached refresh promise to force a new refresh
        this.refreshPromise = null;
        
        // Retry the request
        return this.makeRequest(url, options, attempt + 1);
      }

      return response;
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        throw new Error(`Request timeout after ${this.timeout}ms`);
      }
      throw error;
    }
  }

  async get(url: string, options: RequestInit = {}): Promise<Response> {
    return this.makeRequest(url, { ...options, method: "GET" });
  }

  async post(url: string, data?: any, options: RequestInit = {}): Promise<Response> {
    return this.makeRequest(url, {
      ...options,
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put(url: string, data?: any, options: RequestInit = {}): Promise<Response> {
    return this.makeRequest(url, {
      ...options,
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch(url: string, data?: any, options: RequestInit = {}): Promise<Response> {
    return this.makeRequest(url, {
      ...options,
      method: "PATCH",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete(url: string, options: RequestInit = {}): Promise<Response> {
    return this.makeRequest(url, { ...options, method: "DELETE" });
  }

  // Convenience methods for JSON responses
  async getJson<T = any>(url: string, options: RequestInit = {}): Promise<T> {
    const response = await this.get(url, options);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  }

  async postJson<T = any>(url: string, data?: any, options: RequestInit = {}): Promise<T> {
    const response = await this.post(url, data, options);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  }

  async putJson<T = any>(url: string, data?: any, options: RequestInit = {}): Promise<T> {
    const response = await this.put(url, data, options);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  }

  async patchJson<T = any>(url: string, data?: any, options: RequestInit = {}): Promise<T> {
    const response = await this.patch(url, data, options);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  }

  async deleteJson<T = any>(url: string, options: RequestInit = {}): Promise<T> {
    const response = await this.delete(url, options);
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return response.json();
  }
}

// Export a default instance
export const apiClient = new ApiClient();

// Export the class for custom instances
export { ApiClient };

// Export convenience functions
export const api = {
  get: (url: string, options?: RequestInit) => apiClient.get(url, options),
  post: (url: string, data?: any, options?: RequestInit) => apiClient.post(url, data, options),
  put: (url: string, data?: any, options?: RequestInit) => apiClient.put(url, data, options),
  patch: (url: string, data?: any, options?: RequestInit) => apiClient.patch(url, data, options),
  delete: (url: string, options?: RequestInit) => apiClient.delete(url, options),
  
  // JSON convenience methods
  getJson: <T = any>(url: string, options?: RequestInit) => apiClient.getJson<T>(url, options),
  postJson: <T = any>(url: string, data?: any, options?: RequestInit) => apiClient.postJson<T>(url, data, options),
  putJson: <T = any>(url: string, data?: any, options?: RequestInit) => apiClient.putJson<T>(url, data, options),
  patchJson: <T = any>(url: string, data?: any, options?: RequestInit) => apiClient.patchJson<T>(url, data, options),
  deleteJson: <T = any>(url: string, options?: RequestInit) => apiClient.deleteJson<T>(url, options),
};
