import { RefreshTokenDto, UserLoginOutputDto, isTokenExpired } from "./auth-types";

export interface TokenRefreshResult {
  success: boolean;
  accessToken?: string;
  refreshToken?: string;
  error?: string;
}

/**
 * Refresh access token using refresh token
 */
export async function refreshAccessToken(refreshToken: string): Promise<TokenRefreshResult> {
  try {
    console.log("🔄 Attempting to refresh access token");
    
    // Check if refresh token is expired before making the request
    if (isTokenExpired(refreshToken)) {
      console.error("❌ Refresh token is expired");
      return {
        success: false,
        error: "refresh_token_expired"
      };
    }

    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    if (!apiBaseUrl) {
      console.error("❌ API_BASE_URL not configured");
      return {
        success: false,
        error: "api_not_configured"
      };
    }

    const refreshData: RefreshTokenDto = {
      refresh_token: refreshToken
    };

    console.log("🔄 Making refresh token request to:", `${apiBaseUrl}/auth/refresh-token`);

    const response = await fetch(`${apiBaseUrl}/auth/refresh-token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(refreshData),
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    console.log("🔄 Refresh token response status:", response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ Token refresh failed:", response.status, errorText);
      
      // Handle specific error cases
      if (response.status === 401) {
        return {
          success: false,
          error: "refresh_token_invalid"
        };
      }
      
      return {
        success: false,
        error: `refresh_failed_${response.status}`
      };
    }

    const result: UserLoginOutputDto = await response.json();
    
    console.log("✅ Token refresh successful");
    
    return {
      success: true,
      accessToken: result.access_token,
      refreshToken: result.refresh_token
    };

  } catch (error) {
    console.error("❌ Token refresh error:", error);
    
    if (error instanceof Error) {
      if (error.name === "AbortError") {
        return {
          success: false,
          error: "refresh_timeout"
        };
      }
      
      if (error.message.includes("fetch")) {
        return {
          success: false,
          error: "network_error"
        };
      }
    }
    
    return {
      success: false,
      error: "unknown_error"
    };
  }
}

/**
 * Check if a token refresh is needed and perform it if necessary
 */
export async function checkAndRefreshToken(
  accessToken: string,
  refreshToken: string,
  bufferMinutes: number = 5
): Promise<TokenRefreshResult> {
  try {
    // Check if access token is expired or expiring soon
    if (!isTokenExpired(accessToken)) {
      // Token is still valid, check if it's expiring soon
      const { isTokenExpiringSoon } = await import("./auth-types");
      
      if (!isTokenExpiringSoon(accessToken, bufferMinutes)) {
        console.log("✅ Access token is still valid and not expiring soon");
        return {
          success: true,
          accessToken,
          refreshToken
        };
      }
      
      console.log("⚠️ Access token is expiring soon, refreshing...");
    } else {
      console.log("⚠️ Access token is expired, refreshing...");
    }

    // Refresh the token
    return await refreshAccessToken(refreshToken);
    
  } catch (error) {
    console.error("❌ Error in checkAndRefreshToken:", error);
    return {
      success: false,
      error: "check_refresh_error"
    };
  }
}

/**
 * Handle token refresh errors and determine next action
 */
export function handleTokenRefreshError(error: string): {
  shouldLogout: boolean;
  shouldRetry: boolean;
  message: string;
} {
  switch (error) {
    case "refresh_token_expired":
    case "refresh_token_invalid":
      return {
        shouldLogout: true,
        shouldRetry: false,
        message: "Session expired. Please log in again."
      };
      
    case "network_error":
    case "refresh_timeout":
      return {
        shouldLogout: false,
        shouldRetry: true,
        message: "Network error. Retrying..."
      };
      
    case "api_not_configured":
      return {
        shouldLogout: true,
        shouldRetry: false,
        message: "Configuration error. Please contact support."
      };
      
    default:
      return {
        shouldLogout: false,
        shouldRetry: true,
        message: "Token refresh failed. Retrying..."
      };
  }
}
