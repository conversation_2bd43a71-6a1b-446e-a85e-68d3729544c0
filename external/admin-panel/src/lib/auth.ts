import <PERSON>A<PERSON> from "next-auth";
import Cred<PERSON><PERSON><PERSON><PERSON><PERSON> from "next-auth/providers/credentials";
import {
  UserLoginInputDto,
  UserLoginOutputDto,
  IRAN_COUNTRY_CODE,
  UserRole,
  UserProfileDto,
} from "./auth-types";

declare module "next-auth" {
  interface User {
    accessToken?: string;
    refreshToken?: string;
    profile?: UserProfileDto;
    roles?: UserRole[];
  }
  interface Session {
    accessToken?: string;
    refreshToken?: string;
    user: {
      id: string;
      phone?: string;
      profile?: UserProfileDto;
      roles?: UserRole[];
    };
  }
  interface JWT {
    accessToken?: string;
    refreshToken?: string;
    profile?: UserProfileDto;
    roles?: UserRole[];
    phone?: string;
  }
}

export const { handlers, auth, signIn, signOut } = NextAuth({
  trustHost: true, // Required for development and proxy environments
  basePath: "/api/auth", // Explicitly set base path for API routes
  secret: process.env.NEXTAUTH_SECRET || "fallback-secret-for-development", // Use environment variable with fallback
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        phone: { label: "Phone", type: "text" },
        confirmation_code: { label: "OTP Code", type: "text" },
        device_id: { label: "Device ID", type: "text" },
      },
      async authorize(credentials) {
        if (
          !credentials?.phone ||
          !credentials?.confirmation_code ||
          !credentials?.device_id
        ) {
          return null;
        }

        const phone = credentials.phone as string;
        const confirmationCode = credentials.confirmation_code as string;
        const deviceId = credentials.device_id as string;

        try {
          // Get API base URL from environment variable
          const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

          // Proceed directly with login (phone registration check is handled in the frontend)
          const loginData: UserLoginInputDto = {
            country_code: IRAN_COUNTRY_CODE,
            phone: phone,
            confirmation_code: confirmationCode,
            device_id: deviceId,
          };

          console.log("Login API call:", `${apiBaseUrl}/auth/login`);
          console.log("Login data:", loginData);

          const loginResponse = await fetch(`${apiBaseUrl}/auth/login`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(loginData),
            // Add timeout and other fetch options for better error handling
            signal: AbortSignal.timeout(10000), // 10 second timeout
          });

          console.log("Login response status:", loginResponse.status);

          if (!loginResponse.ok) {
            const errorText = await loginResponse.text();
            console.error("Login failed:", loginResponse.status, errorText);
            return null;
          }

          const loginResult: UserLoginOutputDto = await loginResponse.json();

          // Fetch user profile to get roles and user details
          try {
            console.log(
              "Fetching user profile from:",
              `${apiBaseUrl}/users/profile`
            );
            console.log(
              "Using access token:",
              loginResult.access_token ? "Present" : "Missing"
            );

            const profileResponse = await fetch(`${apiBaseUrl}/users/profile`, {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${loginResult.access_token}`,
              },
              signal: AbortSignal.timeout(10000), // 10 second timeout
            });

            console.log("Profile response status:", profileResponse.status);

            if (profileResponse.ok) {
              const profile: UserProfileDto = await profileResponse.json();
              console.log("User profile fetched successfully:", {
                userId: profile.id,
                roles: profile.roles,
                rolesLength: profile.roles?.length || 0,
                status: profile.status,
                fullProfile: profile,
              });

              return {
                id: profile.id.toString(),
                accessToken: loginResult.access_token,
                refreshToken: loginResult.refresh_token,
                profile: profile,
                roles: profile.roles || [], // Ensure roles is always an array
              };
            } else {
              const errorText = await profileResponse.text();
              console.warn("Failed to fetch user profile:", {
                status: profileResponse.status,
                statusText: profileResponse.statusText,
                error: errorText,
              });
            }
          } catch (profileError) {
            console.error("Error fetching user profile:", profileError);
          }

          // Fallback: return with tokens only if profile fetch fails
          console.warn(
            "Returning user without profile/roles due to profile fetch failure"
          );
          return {
            id: phone, // Use phone as ID since we don't have user ID yet
            accessToken: loginResult.access_token,
            refreshToken: loginResult.refresh_token,
            profile: undefined,
            roles: [], // Explicitly set empty roles array instead of undefined
          };
        } catch (error) {
          console.error("Auth error:", error);

          // Handle different types of errors
          if (error instanceof Error) {
            if (error.name === "AbortError") {
              console.error("Request timeout - API server may be unreachable");
            } else if (error.message.includes("fetch")) {
              console.error("Network error - check API server connectivity");
            }
          }

          return null;
        }
      },
    }),
  ],
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60 * 30, // 24 hours
    updateAge: 60 * 60, // Update session every hour
  },
  // Add additional configuration for better session handling
  useSecureCookies: process.env.NODE_ENV === "production",
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },
  events: {
    async signIn(message) {
      console.log("🔐 SignIn event:", message);
    },
    async signOut(message) {
      console.log("🔐 SignOut event:", message);
    },
    async session(message) {
      console.log("🔐 Session event:", message);
    },
  },
  callbacks: {
    async jwt({ token, user, trigger }) {
      console.log("JWT callback triggered:", {
        trigger,
        hasToken: !!token,
        hasUser: !!user,
        tokenKeys: token ? Object.keys(token) : null,
        userKeys: user ? Object.keys(user) : null,
      });

      // Handle initial login
      if (user) {
        token.accessToken = user.accessToken;
        token.refreshToken = user.refreshToken;
        token.profile = user.profile;
        token.roles = user.roles;
        token.phone = user.profile?.phone || user.id;
        console.log("JWT token updated with user data:", {
          accessToken: !!user.accessToken,
          refreshToken: !!user.refreshToken,
          hasProfile: !!user.profile,
          roles: user.roles,
        });
        return token;
      }

      // Check if we need to refresh the access token
      if (token.accessToken && token.refreshToken) {
        try {
          const { isTokenExpired, isTokenExpiringSoon } = await import(
            "./auth-types"
          );
          const { checkAndRefreshToken } = await import("./token-refresh");

          // Check if access token is expired or expiring soon
          const accessToken = token.accessToken as string;
          const refreshToken = token.refreshToken as string;

          if (
            isTokenExpired(accessToken) ||
            isTokenExpiringSoon(accessToken, 5)
          ) {
            console.log(
              "🔄 JWT callback - Access token expired/expiring, attempting refresh"
            );

            const refreshResult = await checkAndRefreshToken(
              accessToken,
              refreshToken
            );

            if (
              refreshResult.success &&
              refreshResult.accessToken &&
              refreshResult.refreshToken
            ) {
              console.log("✅ JWT callback - Token refresh successful");
              token.accessToken = refreshResult.accessToken;
              token.refreshToken = refreshResult.refreshToken;

              // Update roles from new access token if needed
              try {
                const { decodeJwt } = await import("jose");
                const decodedToken = decodeJwt(refreshResult.accessToken);
                if (decodedToken.roles && Array.isArray(decodedToken.roles)) {
                  token.roles = decodedToken.roles as UserRole[];
                }
              } catch (decodeError) {
                console.warn(
                  "JWT callback - Failed to decode refreshed access token:",
                  decodeError
                );
              }
            } else {
              console.error(
                "❌ JWT callback - Token refresh failed:",
                refreshResult.error
              );
              // Clear tokens to force re-authentication
              token.accessToken = undefined;
              token.refreshToken = undefined;
              token.profile = undefined;
              token.roles = undefined;
              return null; // This will trigger a sign out
            }
          }
        } catch (refreshError) {
          console.error(
            "❌ JWT callback - Error during token refresh:",
            refreshError
          );
        }
      }

      // If we don't have roles but we have an access token, extract roles from the JWT
      if (!token.roles && token.accessToken) {
        try {
          console.log(
            "JWT callback - No roles found, attempting to decode access token"
          );
          const { decodeJwt } = await import("jose");
          const decodedToken = decodeJwt(token.accessToken as string);
          console.log("JWT callback - Decoded access token payload:", {
            id: decodedToken.id,
            username: decodedToken.username,
            roles: decodedToken.roles,
            fullPayload: decodedToken,
          });

          if (decodedToken.roles && Array.isArray(decodedToken.roles)) {
            token.roles = decodedToken.roles as UserRole[];
            console.log(
              "JWT callback - Successfully extracted roles from access token:",
              token.roles
            );
          } else {
            console.warn(
              "JWT callback - No roles found in decoded access token"
            );
          }
        } catch (error) {
          console.error(
            "JWT callback - Failed to decode access token for roles:",
            error
          );
        }
      }

      return token;
    },
    async session({ session, token }) {
      console.log("Session callback triggered:", {
        hasSession: !!session,
        hasToken: !!token,
        tokenKeys: token ? Object.keys(token) : null,
        tokenRoles: token.roles,
        tokenRolesType: typeof token.roles,
      });

      session.accessToken = token.accessToken as string;
      session.refreshToken = token.refreshToken as string;

      // Update user object in session with profile and roles
      session.user = {
        ...session.user,
        id: token.sub || session.user.id,
        phone: token.phone as string,
        profile: token.profile as UserProfileDto,
        roles: token.roles as UserRole[],
      };

      console.log("Session updated with token data:", {
        accessToken: !!session.accessToken,
        refreshToken: !!session.refreshToken,
        hasProfile: !!session.user.profile,
        roles: session.user.roles,
        rolesType: typeof session.user.roles,
        rolesLength: Array.isArray(session.user.roles)
          ? session.user.roles.length
          : "not array",
      });

      return session;
    },
    async signIn({ user, account, profile }) {
      console.log("🔐 SignIn callback triggered:", {
        hasUser: !!user,
        hasAccount: !!account,
        hasProfile: !!profile,
        userId: user?.id,
        accountProvider: account?.provider,
      });

      return true; // Allow sign in
    },
    async redirect({ url, baseUrl }) {
      console.log("🔐 Redirect callback triggered:", { url, baseUrl });

      // If the URL is relative, make it absolute
      if (url.startsWith("/")) {
        const fullUrl = `${baseUrl}${url}`;
        console.log("Redirect: relative URL converted to:", fullUrl);
        return fullUrl;
      }

      // If the URL is on the same origin, allow it
      if (url.startsWith(baseUrl)) {
        console.log("Redirect: same origin URL allowed:", url);
        return url;
      }

      // Default to dashboard for external URLs or unknown cases
      // The /dashboard route will handle role-based redirects
      const defaultUrl = `${baseUrl}/dashboard`;
      console.log(
        "Redirect: external/unknown URL, redirecting to:",
        defaultUrl
      );
      return defaultUrl;
    },
  },
  pages: {
    signIn: "/login",
  },
  debug: process.env.NODE_ENV === "development",
  logger: {
    error: (error: Error, ...args: unknown[]) => {
      console.error("NextAuth Error:", error.message, ...args);
    },
    warn: (message: string) => {
      console.warn("NextAuth Warning:", message);
    },
  },
});
