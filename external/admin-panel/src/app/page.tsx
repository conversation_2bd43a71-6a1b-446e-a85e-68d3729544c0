"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import Loading from "./admin/dashboard/loading";
import { getRoleBasedRedirectUrl } from "@/lib/role-based-navigation";
import { useTranslations } from "next-intl";

export default function Home() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const t = useTranslations();

  useEffect(() => {
    if (status === "loading") return;

    if (session) {
      // Use role-based redirect instead of hardcoded /dashboard
      const redirectUrl = getRoleBasedRedirectUrl(session.user?.roles || []);
      console.log("Root page redirecting authenticated user to:", redirectUrl);
      router.push(redirectUrl);
    } else {
      router.push("/login");
    }
  }, [session, status, router]);

  if (status === "loading") {
    return <Loading />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          {t("common.dashboard.title")}
        </h1>
        <p className="text-gray-600 mb-8">
          {t("common.dashboard.description")}
        </p>
        <div className="space-x-4">
          <Button onClick={() => router.push("/login")}>
            {t("auth.login.signIn")}
          </Button>
        </div>
      </div>
    </div>
  );
}
