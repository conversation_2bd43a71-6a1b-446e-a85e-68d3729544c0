"use client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { useTranslations } from "next-intl";

function DashboardContent() {
  const t = useTranslations("admin.dashboard");
  return (
    <div className="px-4 py-6 sm:px-0">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>{t("cards.totalUsers.title")}</CardTitle>
            <CardDescription>
              {t("cards.totalUsers.description")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">۱,۲۳۴</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>{t("cards.products.title")}</CardTitle>
            <CardDescription>{t("cards.products.description")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">۱</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>{t("cards.sales.title")}</CardTitle>
            <CardDescription>{t("cards.sales.description")}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">۸۹,۴۳۲ تومان</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>{t("cards.discounts.title")}</CardTitle>
            <CardDescription>
              {t("cards.discounts.description")}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">۲۳</div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function DashboardPage() {
  return <DashboardContent />;
}
