"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { useDiscounts } from "@/hooks/admin/useDiscounts";
import { DiscountStats } from "@/components/admin/discounts/DiscountStats";
import { DiscountFilters } from "@/components/admin/discounts/DiscountFilters";
import { DiscountContent } from "@/components/admin/discounts/DiscountContent";
import { DiscountPagination } from "@/components/admin/discounts/DiscountPagination";
import { DiscountQueryParams } from "@/lib/admin/discount-types";

function DiscountListContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations("admin.discount.discounts");

  // Parse URL parameters into initial filters
  const getInitialFilters = (): DiscountQueryParams => {
    const params = new URLSearchParams(searchParams.toString());
    return {
      state:
        (params.get("state") as "ACTIVE" | "DEACTIVE" | "USED") || undefined,
      type: (params.get("type") as "simple" | "referral") || undefined,
      code: params.get("code") || undefined,
      description: params.get("description") || undefined,
      created_date_from: params.get("created_date_from") || undefined,
      created_date_to: params.get("created_date_to") || undefined,
      expire_date_from: params.get("expire_date_from") || undefined,
      expire_date_to: params.get("expire_date_to") || undefined,
      page: params.get("page") ? parseInt(params.get("page")!) : 1,
      limit: params.get("limit") ? parseInt(params.get("limit")!) : 25,
    };
  };

  const {
    discounts,
    stats,
    paginationData,
    loading,
    error,
    loadDiscounts,
    updateFilters,
    changePage,
    changePageSize,
  } = useDiscounts(getInitialFilters());

  const handleCreateNew = () => {
    router.push("/admin/dashboard/generate-discount");
  };

  // Update URL when filters change
  const handleFiltersChange = (filters: Partial<DiscountQueryParams>) => {
    const params = new URLSearchParams();

    // Add filter parameters to URL
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        params.set(key, value.toString());
      }
    });

    // Update URL without triggering a page reload
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, "", newUrl);

    updateFilters(filters);
  };

  // Update URL when pagination changes
  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", page.toString());
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, "", newUrl);
    changePage(page);
  };

  const handlePageSizeChange = (pageSize: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("limit", pageSize.toString());
    params.set("page", "1"); // Reset to first page when changing page size
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, "", newUrl);
    changePageSize(pageSize);
  };

  return (
    <div className="px-4 py-6 sm:px-0">
      {/* Page Header */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">{t("title")}</h2>
        <p className="text-gray-600">{t("subtitle")}</p>
      </div>

      <DiscountStats stats={stats} />

      <DiscountFilters
        onRefresh={loadDiscounts}
        loading={loading}
        onFiltersChange={handleFiltersChange}
        initialFilters={getInitialFilters()}
      />

      <DiscountContent
        loading={loading}
        error={error}
        filteredDiscounts={discounts}
        onRefresh={loadDiscounts}
        onCreateNew={handleCreateNew}
      />

      <DiscountPagination
        paginationData={paginationData}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        loading={loading}
      />
    </div>
  );
}

export default function DiscountListPage() {
  return <DiscountListContent />;
}
