"use client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTranslations } from "next-intl";
import { useState } from "react";

function ProductsContent() {
  const t = useTranslations("admin.products");
  const [searchTerm, setSearchTerm] = useState("");

  // Mock data - replace with actual API call
  const products = [
    {
      id: 1,
      name: "محصول نمونه ۱",
      description: "توضیحات محصول نمونه",
      price: 150000,
      status: "ACTIVE",
      category: "دسته‌بندی ۱",
      created_at: "2024-01-15",
    },
    {
      id: 2,
      name: "محصول نمونه ۲",
      description: "توضیحات محصول دوم",
      price: 250000,
      status: "INACTIVE",
      category: "دسته‌بندی ۲",
      created_at: "2024-01-20",
    },
  ];

  const filteredProducts = products.filter(
    (product) =>
      product.name.includes(searchTerm) ||
      product.description.includes(searchTerm) ||
      product.category.includes(searchTerm)
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            مدیریت محصولات
          </h2>
          <p className="text-gray-600">
            مشاهده و مدیریت محصولات فروشگاه
          </p>
        </div>
        <Button>افزودن محصول جدید</Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>جستجو و فیلتر</CardTitle>
          <CardDescription>
            محصولات را بر اساس نام، توضیحات یا دسته‌بندی جستجو کنید
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Input
            placeholder="جستجو محصولات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>لیست محصولات</CardTitle>
          <CardDescription>
            {filteredProducts.length} محصول یافت شد
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-right p-2">نام محصول</th>
                  <th className="text-right p-2">توضیحات</th>
                  <th className="text-right p-2">قیمت</th>
                  <th className="text-right p-2">دسته‌بندی</th>
                  <th className="text-right p-2">وضعیت</th>
                  <th className="text-right p-2">تاریخ ایجاد</th>
                  <th className="text-right p-2">عملیات</th>
                </tr>
              </thead>
              <tbody>
                {filteredProducts.map((product) => (
                  <tr key={product.id} className="border-b hover:bg-gray-50">
                    <td className="p-2 font-medium">{product.name}</td>
                    <td className="p-2 text-gray-600 max-w-xs truncate">
                      {product.description}
                    </td>
                    <td className="p-2">
                      {product.price.toLocaleString("fa-IR")} تومان
                    </td>
                    <td className="p-2">{product.category}</td>
                    <td className="p-2">
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          product.status === "ACTIVE"
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {product.status === "ACTIVE" ? "فعال" : "غیرفعال"}
                      </span>
                    </td>
                    <td className="p-2">{product.created_at}</td>
                    <td className="p-2">
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          ویرایش
                        </Button>
                        <Button variant="outline" size="sm">
                          حذف
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function ProductsPage() {
  return <ProductsContent />;
}
