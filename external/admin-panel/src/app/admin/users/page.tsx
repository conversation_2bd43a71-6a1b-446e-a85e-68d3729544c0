"use client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTranslations } from "next-intl";
import { useState } from "react";

function UsersContent() {
  const t = useTranslations("admin.users");
  const [searchTerm, setSearchTerm] = useState("");

  // Mock data - replace with actual API call
  const users = [
    {
      id: 1,
      name: "احمد محمدی",
      phone: "+98912345678",
      email: "<EMAIL>",
      status: "ACTIVE",
      roles: ["USER"],
      created_at: "2024-01-15",
    },
    {
      id: 2,
      name: "فاطمه احمدی",
      phone: "+98912345679",
      email: "<EMAIL>",
      status: "ACTIVE",
      roles: ["AFFILIATE"],
      created_at: "2024-01-20",
    },
  ];

  const filteredUsers = users.filter(
    (user) =>
      user.name.includes(searchTerm) ||
      user.phone.includes(searchTerm) ||
      user.email.includes(searchTerm)
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            مدیریت کاربران
          </h2>
          <p className="text-gray-600">
            مشاهده و مدیریت کاربران سیستم
          </p>
        </div>
        <Button>افزودن کاربر جدید</Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>جستجو و فیلتر</CardTitle>
          <CardDescription>
            کاربران را بر اساس نام، تلفن یا ایمیل جستجو کنید
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Input
            placeholder="جستجو کاربران..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>لیست کاربران</CardTitle>
          <CardDescription>
            {filteredUsers.length} کاربر یافت شد
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-right p-2">نام</th>
                  <th className="text-right p-2">تلفن</th>
                  <th className="text-right p-2">ایمیل</th>
                  <th className="text-right p-2">وضعیت</th>
                  <th className="text-right p-2">نقش</th>
                  <th className="text-right p-2">تاریخ عضویت</th>
                  <th className="text-right p-2">عملیات</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="border-b hover:bg-gray-50">
                    <td className="p-2 font-medium">{user.name}</td>
                    <td className="p-2">{user.phone}</td>
                    <td className="p-2">{user.email}</td>
                    <td className="p-2">
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          user.status === "ACTIVE"
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {user.status === "ACTIVE" ? "فعال" : "غیرفعال"}
                      </span>
                    </td>
                    <td className="p-2">
                      {user.roles.map((role) => (
                        <span
                          key={role}
                          className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs mr-1"
                        >
                          {role === "ADMIN"
                            ? "مدیر"
                            : role === "AFFILIATE"
                            ? "همکار"
                            : "کاربر"}
                        </span>
                      ))}
                    </td>
                    <td className="p-2">{user.created_at}</td>
                    <td className="p-2">
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          ویرایش
                        </Button>
                        <Button variant="outline" size="sm">
                          حذف
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function UsersPage() {
  return <UsersContent />;
}
