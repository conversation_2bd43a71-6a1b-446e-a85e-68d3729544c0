"use client";

import { useSession } from "next-auth/react";
import AuthGuard from "@/components/auth-guard";
import TokenStatusDebug, {
  TokenRefreshTester,
} from "@/components/token-status-debug";
import { api } from "@/lib/api-client";
import { useState } from "react";

export default function TokenTestPage() {
  const { data: session } = useSession();
  const [apiTestResult, setApiTestResult] = useState<string | null>(null);
  const [isTestingApi, setIsTestingApi] = useState(false);

  const testApiCall = async () => {
    setIsTestingApi(true);
    setApiTestResult(null);

    try {
      // Test API call to a protected endpoint
      const response = await api.get("/users/profile");

      if (response.ok) {
        const data = await response.json();
        setApiTestResult(
          `✅ API call successful: ${JSON.stringify(data, null, 2)}`
        );
      } else {
        setApiTestResult(
          `❌ API call failed: ${response.status} ${response.statusText}`
        );
      }
    } catch (error) {
      setApiTestResult(
        `❌ API call error: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      setIsTestingApi(false);
    }
  };

  return (
    <AuthGuard requiredRoles={["ADMIN"]}>
      <div className="container mx-auto p-6 space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Token Expiration Test Page
          </h1>
          <p className="text-gray-600 mb-6">
            This page helps test the token expiration and refresh functionality.
            Use the debug components below to monitor token status and test
            refresh behavior.
          </p>
        </div>

        {/* Token Status Debug */}
        <TokenStatusDebug />

        {/* Token Refresh Tester */}
        <TokenRefreshTester />

        {/* API Test */}
        <div className="p-4 bg-green-50 rounded-lg">
          <h3 className="font-bold text-green-800 mb-2">API Client Test</h3>
          <p className="text-sm text-green-700 mb-4">
            Test the API client with automatic token refresh on 401 responses.
          </p>

          <button
            onClick={testApiCall}
            disabled={isTestingApi}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
          >
            {isTestingApi ? "Testing..." : "Test API Call"}
          </button>

          {apiTestResult && (
            <div className="mt-4 p-3 bg-white rounded border">
              <h4 className="font-semibold mb-2">API Test Result:</h4>
              <pre className="text-sm overflow-x-auto whitespace-pre-wrap">
                {apiTestResult}
              </pre>
            </div>
          )}
        </div>

        {/* Session Info */}
        <div className="p-4 bg-purple-50 rounded-lg">
          <h3 className="font-bold text-purple-800 mb-2">
            Session Information
          </h3>
          <div className="text-sm space-y-2">
            <div>
              <strong>User ID:</strong> {session?.user?.id || "N/A"}
            </div>
            <div>
              <strong>Phone:</strong> {session?.user?.phone || "N/A"}
            </div>
            <div>
              <strong>Roles:</strong>{" "}
              {session?.user?.roles?.join(", ") || "N/A"}
            </div>
            <div>
              <strong>Session Expires:</strong> {session?.expires || "N/A"}
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="p-4 bg-yellow-50 rounded-lg">
          <h3 className="font-bold text-yellow-800 mb-2">
            Testing Instructions
          </h3>
          <div className="text-sm text-yellow-700 space-y-2">
            <p>
              <strong>1. Monitor Token Status:</strong> Watch the &quot;Token
              Status Debug&quot; component to see real-time token expiration
              information.
            </p>
            <p>
              <strong>2. Test Manual Refresh:</strong> Use the &quot;Manual
              Refresh&quot; button to trigger token refresh manually.
            </p>
            <p>
              <strong>3. Test API Calls:</strong> Use the &quot;Test API
              Call&quot; button to test automatic token refresh on API requests.
            </p>
            <p>
              <strong>4. Wait for Expiration:</strong> Leave the page open and
              watch as the token approaches expiration. The system should
              automatically refresh it.
            </p>
            <p>
              <strong>5. Check Console:</strong> Monitor the browser console for
              detailed logging of token refresh operations.
            </p>
          </div>
        </div>

        {/* Raw Session Data */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="font-bold text-gray-800 mb-2">Raw Session Data</h3>
          <pre className="text-xs overflow-x-auto bg-white p-3 rounded border">
            {JSON.stringify(session, null, 2)}
          </pre>
        </div>
      </div>
    </AuthGuard>
  );
}
