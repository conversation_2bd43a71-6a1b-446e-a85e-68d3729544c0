import { api } from "@/lib/api-client";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Try to reach the external API
    const response = await api.get("/");

    return NextResponse.json({
      status: "ok",
      message: "Health check passed",
      apiAccessible: response.ok,
      apiStatus: response.status,
      timestamp: new Date().toISOString(),
      config: {
        nextAuthUrl: process.env.NEXTAUTH_URL,
        nodeEnv: process.env.NODE_ENV,
      },
    });
  } catch (error) {
    console.error("Health check failed:", error);
    return NextResponse.json(
      {
        status: "error",
        message: "Health check failed",
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
