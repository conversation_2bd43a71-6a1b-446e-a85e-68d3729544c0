"use client";

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTranslations } from "next-intl";
import { useState } from "react";

function CommissionsContent() {
  const t = useTranslations("affiliate.commissions");
  const [searchTerm, setSearchTerm] = useState("");

  // Mock data - replace with actual API call
  const commissions = [
    {
      id: 1,
      referral_name: "احمد محمدی",
      order_id: "ORD-001",
      product_name: "محصول نمونه ۱",
      order_amount: 150000,
      commission_rate: 10,
      commission_amount: 15000,
      status: "PAID",
      created_at: "2024-01-15",
      paid_at: "2024-01-20",
    },
    {
      id: 2,
      referral_name: "فاطمه احمدی",
      order_id: "ORD-002",
      product_name: "محصول نمونه ۲",
      order_amount: 250000,
      commission_rate: 12,
      commission_amount: 30000,
      status: "PENDING",
      created_at: "2024-01-18",
      paid_at: null,
    },
  ];

  const filteredCommissions = commissions.filter(
    (commission) =>
      commission.referral_name.includes(searchTerm) ||
      commission.order_id.includes(searchTerm) ||
      commission.product_name.includes(searchTerm)
  );

  const totalCommissions = commissions.reduce(
    (sum, commission) => sum + commission.commission_amount,
    0
  );
  const paidCommissions = commissions
    .filter((c) => c.status === "PAID")
    .reduce((sum, commission) => sum + commission.commission_amount, 0);
  const pendingCommissions = commissions
    .filter((c) => c.status === "PENDING")
    .reduce((sum, commission) => sum + commission.commission_amount, 0);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">
          کمیسیون‌های من
        </h2>
        <p className="text-gray-600">
          مشاهده و پیگیری کمیسیون‌های دریافتی
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>کل کمیسیون</CardTitle>
            <CardDescription>مجموع کل کمیسیون‌ها</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {totalCommissions.toLocaleString("fa-IR")} تومان
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>پرداخت شده</CardTitle>
            <CardDescription>کمیسیون‌های پرداخت شده</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {paidCommissions.toLocaleString("fa-IR")} تومان
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>در انتظار پرداخت</CardTitle>
            <CardDescription>کمیسیون‌های در انتظار</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {pendingCommissions.toLocaleString("fa-IR")} تومان
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>جستجو و فیلتر</CardTitle>
          <CardDescription>
            کمیسیون‌ها را بر اساس نام ارجاع، شماره سفارش یا محصول جستجو کنید
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Input
            placeholder="جستجو کمیسیون‌ها..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>لیست کمیسیون‌ها</CardTitle>
          <CardDescription>
            {filteredCommissions.length} کمیسیون یافت شد
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-right p-2">نام ارجاع</th>
                  <th className="text-right p-2">شماره سفارش</th>
                  <th className="text-right p-2">محصول</th>
                  <th className="text-right p-2">مبلغ سفارش</th>
                  <th className="text-right p-2">نرخ کمیسیون</th>
                  <th className="text-right p-2">مبلغ کمیسیون</th>
                  <th className="text-right p-2">وضعیت</th>
                  <th className="text-right p-2">تاریخ ایجاد</th>
                </tr>
              </thead>
              <tbody>
                {filteredCommissions.map((commission) => (
                  <tr key={commission.id} className="border-b hover:bg-gray-50">
                    <td className="p-2 font-medium">{commission.referral_name}</td>
                    <td className="p-2">{commission.order_id}</td>
                    <td className="p-2">{commission.product_name}</td>
                    <td className="p-2">
                      {commission.order_amount.toLocaleString("fa-IR")} تومان
                    </td>
                    <td className="p-2">{commission.commission_rate}%</td>
                    <td className="p-2 font-medium">
                      {commission.commission_amount.toLocaleString("fa-IR")} تومان
                    </td>
                    <td className="p-2">
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          commission.status === "PAID"
                            ? "bg-green-100 text-green-800"
                            : "bg-orange-100 text-orange-800"
                        }`}
                      >
                        {commission.status === "PAID" ? "پرداخت شده" : "در انتظار"}
                      </span>
                    </td>
                    <td className="p-2">{commission.created_at}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function CommissionsPage() {
  return <CommissionsContent />;
}
