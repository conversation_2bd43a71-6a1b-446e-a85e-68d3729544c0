"use client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

function ReportsContent() {
  const t = useTranslations("affiliate.reports");

  // Mock data - replace with actual API call
  const monthlyStats = [
    { month: "دی ۱۴۰۳", referrals: 45, sales: 2500000, commission: 250000 },
    { month: "آذر ۱۴۰۳", referrals: 38, sales: 2100000, commission: 210000 },
    { month: "آبان ۱۴۰۳", referrals: 52, sales: 2800000, commission: 280000 },
    { month: "مهر ۱۴۰۳", referrals: 41, sales: 2300000, commission: 230000 },
  ];

  const topProducts = [
    { name: "محصول نمونه ۱", sales: 15, commission: 150000 },
    { name: "محصول نمونه ۲", sales: 12, commission: 120000 },
    { name: "محصول نمونه ۳", sales: 8, commission: 80000 },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            گزارش‌های همکاری
          </h2>
          <p className="text-gray-600">
            مشاهده آمار و گزارش‌های تفصیلی عملکرد
          </p>
        </div>
        <Button>دانلود گزارش PDF</Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>آمار ماهانه</CardTitle>
            <CardDescription>
              عملکرد شما در ۴ ماه اخیر
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {monthlyStats.map((stat, index) => (
                <div key={index} className="border-b pb-4 last:border-b-0">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium">{stat.month}</h4>
                    <span className="text-sm text-gray-600">
                      {stat.referrals} ارجاع
                    </span>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">فروش</p>
                      <p className="font-medium">
                        {stat.sales.toLocaleString("fa-IR")} تومان
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">کمیسیون</p>
                      <p className="font-medium text-green-600">
                        {stat.commission.toLocaleString("fa-IR")} تومان
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>محصولات پرفروش</CardTitle>
            <CardDescription>
              محصولاتی که بیشترین فروش را داشته‌اند
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProducts.map((product, index) => (
                <div key={index} className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">{product.name}</p>
                    <p className="text-sm text-gray-600">
                      {product.sales} فروش
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-green-600">
                      {product.commission.toLocaleString("fa-IR")} تومان
                    </p>
                    <p className="text-sm text-gray-600">کمیسیون</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>خلاصه عملکرد</CardTitle>
          <CardDescription>
            آمار کلی عملکرد شما تا کنون
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">۱۷۶</div>
              <div className="text-sm text-gray-600">کل ارجاعات</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">۹,۷۰۰,۰۰۰</div>
              <div className="text-sm text-gray-600">کل فروش (تومان)</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">۹۷۰,۰۰۰</div>
              <div className="text-sm text-gray-600">کل کمیسیون (تومان)</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">۵.۵%</div>
              <div className="text-sm text-gray-600">میانگین نرخ تبدیل</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>نکات بهبود عملکرد</CardTitle>
          <CardDescription>
            پیشنهادهایی برای افزایش درآمد همکاری
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <p className="text-sm">
                با اشتراک‌گذاری منظم محتوا در شبکه‌های اجتماعی، تعداد ارجاعات خود را افزایش دهید.
              </p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <p className="text-sm">
                روی محصولات پرفروش تمرکز کنید تا کمیسیون بیشتری کسب کنید.
              </p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
              <p className="text-sm">
                از ابزارهای تحلیل ترافیک استفاده کنید تا بهترین زمان‌ها برای تبلیغ را شناسایی کنید.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function ReportsPage() {
  return <ReportsContent />;
}
