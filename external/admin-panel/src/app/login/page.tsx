"use client";

import { useState, useEffect } from "react";
import { signIn, useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  CheckRegisterPhoneInputDto,
  CheckPhoneRegisteredOutputDto,
  IRAN_COUNTRY_CODE,
  IRAN_COUNTRY_NAME,
} from "@/lib/auth-types";
import Loading from "../admin/dashboard/loading";
import { useTranslations } from "next-intl";
import { getRoleBasedRedirectUrl } from "@/lib/role-based-navigation";

export default function LoginPage() {
  const t = useTranslations("auth");
  const [phone, setPhone] = useState("");
  const [otp, setOtp] = useState("");
  const [step, setStep] = useState<"phone" | "otp">("phone");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { data: session, status } = useSession();

  // Redirect authenticated users based on their roles
  useEffect(() => {
    console.log("Login page - Auth check:", { status, hasSession: !!session });

    if (status === "loading") return; // Still loading

    if (session) {
      console.log("User already authenticated, redirecting based on roles");
      console.log("Session data:", {
        userId: session.user?.id,
        roles: session.user?.roles,
        hasAccessToken: !!session.accessToken,
        hasRefreshToken: !!session.refreshToken,
      });

      // Get role-based redirect URL
      const redirectUrl = getRoleBasedRedirectUrl(session.user?.roles || []);
      console.log("Redirecting to:", redirectUrl);
      router.push(redirectUrl);
    }
  }, [session, status, router]);

  // Show loading while checking authentication status
  if (status === "loading") {
    return <Loading />;
  }

  // If user is authenticated, don't show the login form (redirect is handled in useEffect)
  if (session) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">{t("login.redirectToDashboard")}</p>
        </div>
      </div>
    );
  }

  // Handle return URL from query parameters
  const searchParams = new URLSearchParams(
    typeof window !== "undefined" ? window.location.search : ""
  );
  const returnUrl = searchParams.get("returnUrl");

  const handleSendOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
      console.log("API Base URL:", apiBaseUrl);
      console.log("Checking phone registration for:", phone);

      // First, test if the API server is accessible
      try {
        const healthCheck = await fetch(`${apiBaseUrl}/`, {
          method: "GET",
          signal: AbortSignal.timeout(3000),
        });
        console.log("API server health check:", healthCheck.status);
      } catch (healthError) {
        console.warn("API server health check failed:", healthError);
        setError(
          "API server is not accessible. Please check if the backend server is running."
        );
        return;
      }

      const phoneCheckData: CheckRegisterPhoneInputDto = {
        country_name: IRAN_COUNTRY_NAME,
        country_code: IRAN_COUNTRY_CODE,
        phone: phone,
      };

      console.log(
        "Phone check request:",
        `${apiBaseUrl}/auth/is-phone-registered`,
        phoneCheckData
      );

      const response = await fetch(`${apiBaseUrl}/auth/is-phone-registered`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(phoneCheckData),
      });

      console.log("Phone check response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Phone check failed:", response.status, errorText);
        throw new Error(
          `Failed to check phone registration: ${response.status}`
        );
      }

      const result: CheckPhoneRegisteredOutputDto = await response.json();

      if (result.status === "SendSms" || result.status === "SendEmail") {
        setStep("otp");
      } else {
        setError(t("login.phoneNotRegistered"));
      }
    } catch {
      setError(t("login.error"));
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      console.log("Attempting login with:", { phone, confirmation_code: otp });

      const result = await signIn("credentials", {
        phone,
        confirmation_code: otp,
        device_id: "admin-panel-device", // Fixed device ID for admin panel
        redirect: false,
      });

      console.log("SignIn result:", result);

      if (result?.error) {
        console.error("Login error:", result.error);
        setError(t("login.invalidOtp"));
      } else if (result?.ok) {
        console.log("Login successful, redirecting...");

        // Wait a moment for the session to be updated, then redirect
        setTimeout(() => {
          // If there's a return URL, use it; otherwise, use role-based redirect
          if (returnUrl) {
            console.log("Redirecting to return URL:", returnUrl);
            router.push(returnUrl);
          } else {
            // We need to get the session to determine roles
            // For now, we'll redirect to a generic dashboard and let the session effect handle it
            console.log(
              "Redirecting to dashboard, session effect will handle role-based redirect"
            );
            router.push("/dashboard");
          }
        }, 100);
      } else {
        console.error("Unexpected signIn result:", result);
        setError(t("login.error"));
      }
    } catch (err) {
      console.error("Login exception:", err);
      setError(t("login.error"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>{t("login.title")}</CardTitle>
          <CardDescription>
            {step === "phone" ? t("login.enterPhone") : t("login.enterOtp")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {step === "phone" ? (
            <form onSubmit={handleSendOtp} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="phone">{t("login.phone")}</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  placeholder="9123456789"
                  required
                  dir="ltr"
                />
              </div>
              {error && <div className="text-red-600 text-sm">{error}</div>}
              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? t("login.sendingOtp") : t("login.sendOtp")}
              </Button>
            </form>
          ) : (
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="otp">{t("login.otp")}</Label>
                <Input
                  id="otp"
                  type="text"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  placeholder="123456"
                  required
                  dir="ltr"
                  maxLength={6}
                />
              </div>
              {error && <div className="text-red-600 text-sm">{error}</div>}
              <div className="space-y-2">
                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? t("login.signingIn") : t("login.signIn")}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    setStep("phone");
                    setOtp("");
                    setError("");
                  }}
                >
                  {t("login.returnToPreviousStep")}
                </Button>
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
