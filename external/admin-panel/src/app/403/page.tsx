"use client";

import { Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useTranslations } from "next-intl";
import { useUser } from "@/contexts/user-context";
import { signOut } from "next-auth/react";
import { getHomeUrl } from "@/lib/role-based-navigation";

function ForbiddenContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const t = useTranslations("common");
  const { user, roles, isAuthenticated } = useUser();

  const reason = searchParams.get("reason") || "access_denied";
  const returnUrl = searchParams.get("returnUrl");

  const getReasonMessage = () => {
    switch (reason) {
      case "admin_required":
        return {
          title: "دسترسی مدیر مورد نیاز",
          description: "برای دسترسی به این بخش، نیاز به نقش مدیر دارید.",
        };
      case "affiliate_required":
        return {
          title: "دسترسی همکار مورد نیاز",
          description: "برای دسترسی به این بخش، نیاز به نقش همکار دارید.",
        };
      case "insufficient_roles":
        return {
          title: "نقش کافی ندارید",
          description: "شما نقش‌های لازم برای دسترسی به این بخش را ندارید.",
        };
      default:
        return {
          title: "دسترسی غیرمجاز",
          description: "شما مجوز دسترسی به این صفحه را ندارید.",
        };
    }
  };

  const { title, description } = getReasonMessage();

  const handleGoBack = () => {
    if (returnUrl) {
      router.push(returnUrl);
    } else {
      router.back();
    }
  };

  const handleGoHome = () => {
    // Use role-based navigation utility
    const homeUrl = getHomeUrl(roles);
    router.push(homeUrl);
  };

  const handleSignOut = async () => {
    await signOut({ callbackUrl: "/login" });
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <svg
              className="w-8 h-8 text-red-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            {title}
          </CardTitle>
          <CardDescription className="text-gray-600 mt-2">
            {description}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {isAuthenticated && user && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">
                اطلاعات کاربری:
              </h4>
              <p className="text-sm text-blue-800">
                <strong>نام:</strong> {user.name} {user.family}
              </p>
              <p className="text-sm text-blue-800">
                <strong>نقش‌ها:</strong>{" "}
                {roles
                  .map((role) => {
                    switch (role) {
                      case "ADMIN":
                        return "مدیر";
                      case "AFFILIATE":
                        return "همکار";
                      case "USER":
                        return "کاربر";
                      default:
                        return role;
                    }
                  })
                  .join("، ")}
              </p>
            </div>
          )}

          <div className="space-y-3">
            <Button onClick={handleGoHome} className="w-full">
              بازگشت به صفحه اصلی
            </Button>

            {returnUrl && (
              <Button
                onClick={handleGoBack}
                variant="outline"
                className="w-full"
              >
                بازگشت به صفحه قبل
              </Button>
            )}

            {isAuthenticated && (
              <Button
                onClick={handleSignOut}
                variant="outline"
                className="w-full text-red-600 border-red-200 hover:bg-red-50"
              >
                خروج از حساب کاربری
              </Button>
            )}
          </div>

          <div className="text-center text-sm text-gray-500 mt-6">
            <p>در صورت بروز مشکل، با پشتیبانی تماس بگیرید.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function ForbiddenPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardContent className="p-6">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
                <p className="text-gray-600">در حال بارگذاری...</p>
              </div>
            </CardContent>
          </Card>
        </div>
      }
    >
      <ForbiddenContent />
    </Suspense>
  );
}
