"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Loading from "../admin/dashboard/loading";
import { getRoleBasedRedirectUrl } from "@/lib/role-based-navigation";

/**
 * Generic dashboard page that redirects users to their role-specific dashboard
 * This acts as a router based on user roles
 */
export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    console.log("Dashboard page - Auth check:", {
      status,
      hasSession: !!session,
    });

    if (status === "loading") return; // Still loading

    if (!session) {
      console.log("No session found, redirecting to login");
      router.push("/login");
      return;
    }

    // Get user roles and redirect accordingly
    const userRoles = session.user?.roles || [];
    const redirectUrl = getRoleBasedRedirectUrl(session.user?.roles ?? []);

    console.log("Dashboard redirect:", {
      userRoles,
      redirectUrl,
      userId: session.user?.id,
    });

    // Only redirect if we're not already on the target page
    if (window.location.pathname !== redirectUrl) {
      router.push(redirectUrl);
    }
  }, [session, status, router]);

  // Show loading while determining redirect
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <Loading />
        <p className="mt-4 text-gray-600">
          {status === "loading"
            ? "در حال بررسی احراز هویت..."
            : "در حال هدایت به داشبورد مناسب..."}
        </p>
      </div>
    </div>
  );
}
