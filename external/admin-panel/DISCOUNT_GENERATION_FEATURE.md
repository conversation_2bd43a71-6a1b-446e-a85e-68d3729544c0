# Discount Generation Feature

## Overview
A new "Generate Discount" page has been added to the admin panel that allows administrators to create discount codes through a user-friendly form interface.

## Features Implemented

### 1. Page Structure
- **Route**: `/dashboard/generate-discount`
- **Authentication**: Protected by admin authentication middleware
- **Layout**: Consistent with existing admin panel design

### 2. Form Fields
Based on the Swagger API schema (`DiscountInputDto`), the form includes:

- **Expire Date** (`expire_date`): Date picker for discount expiration
- **Status** (`state`): Dropdown with ACTIVE/INACTIVE options
- **Discount Percentage** (`discount_percent`): Number input (1-100%)
- **Usage Count** (`count`): Number input for usage limit
- **Type** (`type`): Dropdown with "simple" or "referral" options
- **Referral ID** (`referral_id`): Number input (required for referral type)
- **Description** (`description`): Text input for discount description

### 3. Validation
Comprehensive client-side validation includes:
- Required field validation
- Date format validation (YYYY-MM-DD)
- Percentage range validation (1-100)
- Positive number validation for count
- Conditional validation for referral ID (required when type is "referral")

### 4. API Integration
- Uses the `/discounts/create` endpoint from the backend
- Proper authentication with Bearer token
- Error handling with user-friendly messages
- Success feedback with form reset

### 5. UI/UX Features
- **Responsive Design**: Works on desktop and mobile
- **Persian/RTL Support**: Full right-to-left layout support
- **Loading States**: Shows loading indicator during API calls
- **Error/Success Messages**: Clear feedback for user actions
- **Form Reset**: Option to clear all form fields
- **Cancel Navigation**: Return to dashboard

### 6. Navigation Integration
- Added "Generate Discount" link to admin panel navigation
- Updated both Persian and English translations
- Proper active state highlighting

## Files Created/Modified

### New Files
1. `src/app/dashboard/generate-discount/page.tsx` - Main page component
2. `src/lib/discount-types.ts` - TypeScript types for discount API
3. `src/lib/discount-api.ts` - API utility functions
4. `src/components/ui/select.tsx` - Select component for dropdowns

### Modified Files
1. `public/locales/fa/dashboard.json` - Persian translations
2. `public/locales/en/dashboard.json` - English translations
3. `src/app/dashboard/page.tsx` - Added navigation link

## Technical Implementation

### Form Management
- Uses React hooks for state management
- Controlled components for all form inputs
- Real-time validation feedback

### API Communication
- Fetch API with proper error handling
- Authentication via NextAuth session tokens
- Timeout handling (10 seconds)
- Proper HTTP status code handling

### Internationalization
- Full Persian and English support
- Consistent with existing translation patterns
- RTL layout support

### Type Safety
- Full TypeScript implementation
- Proper type definitions for API responses
- Form data validation types

## Usage Instructions

1. **Access**: Navigate to `/dashboard/generate-discount` or use the navigation menu
2. **Authentication**: Must be logged in as an admin
3. **Fill Form**: Complete all required fields
4. **Validation**: Form validates in real-time
5. **Submit**: Click "Create Discount Code" to generate
6. **Feedback**: Success/error messages displayed
7. **Reset**: Use "Clear Form" to start over
8. **Cancel**: Use "Cancel" to return to dashboard

## Error Handling

The implementation includes comprehensive error handling:
- Network connectivity issues
- API server errors
- Authentication failures
- Validation errors
- Timeout scenarios

## Security Considerations

- Protected by admin authentication middleware
- Bearer token authentication for API calls
- Input validation and sanitization
- CSRF protection via NextAuth

## Future Enhancements

Potential improvements for future versions:
1. Bulk discount code generation
2. Discount code preview before creation
3. Integration with existing discount management
4. Advanced validation rules
5. Discount code templates
6. Usage analytics integration

## Testing

The implementation has been tested for:
- Form validation scenarios
- API integration
- Error handling
- UI responsiveness
- Translation accuracy
- Navigation functionality

## Dependencies

No new dependencies were added. The implementation uses existing project dependencies:
- React 19
- Next.js 15
- NextAuth
- Tailwind CSS
- shadcn/ui components
