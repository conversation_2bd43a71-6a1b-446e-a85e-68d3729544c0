# Memory Bank — Repeatable Tasks

## Add a New Protected Page
Last performed: 2025-09-16

Files to modify:
- Add page under role route: [src/app/admin/feature/page.tsx](src/app/admin/feature/page.tsx)
- Ensure role layout wraps content: [src/app/admin/layout.tsx](src/app/admin/layout.tsx), [src/app/affiliate/layout.tsx](src/app/affiliate/layout.tsx)
- Use role guard in layout or page: [src/components/auth-guard.tsx](src/components/auth-guard.tsx)
- Add translations: [messages/fa/common.json](messages/fa/common.json), [messages/en/common.json](messages/en/common.json) and feature files (e.g. [messages/fa/admin/dashboard.json](messages/fa/admin/dashboard.json))

Steps:
1. Create the page component under the correct role path (e.g. /admin/feature).
2. Verify the role layout already wraps children with `<AuthGuard requiredRoles={["ADMIN"]}>` or add the guard in the page.
3. Reference translations using useTranslations in components.
4. Add required i18n keys in FA/EN message files.
5. Navigate to the route to confirm role protection and translations render.

Important notes:
- Prefer placing the guard in the role layout to avoid duplication.
- Keep all user-facing strings in i18n files.

## Add an API Wrapper + Hook
Last performed: 2025-09-16

Files to modify:
- API client usage: [src/lib/api-client.ts](src/lib/api-client.ts)
- Feature API wrapper: [src/lib/admin](src/lib/admin)
- React hook for data: [src/hooks](src/hooks)

Steps:
1. Implement a typed API wrapper that calls api.getJson/postJson and normalizes errors (example: [src/lib/admin/discount-api.ts](src/lib/admin/discount-api.ts)).
2. Create a hook that calls the wrapper, handles loading/error, and exposes typed state (example: [src/hooks/admin/useDiscounts.ts](src/hooks/admin/useDiscounts.ts)).
3. In the page/component, use the hook and wire up filters/pagination.
4. Add any required i18n messages for UI strings.

Important notes:
- The API client attaches Authorization automatically and refreshes tokens if needed.
- Use AppApiError for normalized error display.

## Implement a Filtered List with Pagination
Last performed: 2025-09-16

Reference example:
- Page: [src/app/admin/dashboard/discounts/page.tsx](src/app/admin/dashboard/discounts/page.tsx)
- Hook: [src/hooks/admin/useDiscounts.ts](src/hooks/admin/useDiscounts.ts)
- UI Components: [src/components/admin/discounts/DiscountContent.tsx](src/components/admin/discounts/DiscountContent.tsx)

Steps:
1. Model query params and map them to URLSearchParams for deep-linkable filters.
2. Build a hook that merges local filter state with URL params and triggers reloads.
3. Implement pagination with page and limit, updating URL for consistency.
4. Provide loading, empty state, and error states using i18n.

Important notes:
- Keep filter updates idempotent and avoid full page reloads via history.replaceState.
- Localize all labels and messages.

## Add i18n Keys for a Feature
Last performed: 2025-09-16

Files to modify:
- Feature messages: e.g., [messages/fa/admin/discount/discounts.json](messages/fa/admin/discount/discounts.json)
- Server loader: [src/i18n/request.ts](src/i18n/request.ts)

Steps:
1. Define nested keys in FA and EN under the appropriate domain (admin, affiliate, common, auth).
2. Ensure getRequestConfig loads the new file under the correct locale object.
3. Consume the keys with useTranslations in components.

Important notes:
- Keep nesting shallow and meaningful (domain.feature.section).
- Avoid hardcoding strings in components.

## Debug and Verify Token Refresh
Last performed: 2025-09-16

Files to use:
- JWT refresh logic: [src/lib/token-refresh.ts](src/lib/token-refresh.ts)
- JWT/session callbacks: [src/lib/auth.ts](src/lib/auth.ts)
- Client hook: [src/hooks/use-token-refresh.ts](src/hooks/use-token-refresh.ts)
- Debug component: [src/components/token-status-debug.tsx](src/components/token-status-debug.tsx)

Steps:
1. Mount TokenStatusDebug on a protected page to visualize expiry and refresh.
2. Confirm Middleware behavior: [src/middleware.ts](src/middleware.ts) adds X-Token-Refresh-Needed when near expiry.
3. Observe NextAuth jwt callback performing refresh when needed.
4. Validate that session.user.roles remains accurate post-refresh.

Important notes:
- Configure NEXT_PUBLIC_API_BASE_URL and NEXTAUTH_SECRET for end-to-end checks.
- Network timeouts are set; watch console logs for cause analysis.

## Fix Affiliate AuthGuard Prop Mismatch
Last performed: 2025-09-16

Files to modify:
- Affiliate layout usage: [src/app/affiliate/layout.tsx](src/app/affiliate/layout.tsx:15)
- AuthGuard props: [src/components/auth-guard.tsx](src/components/auth-guard.tsx)

Steps:
1. Replace `<AuthGuard requireAffiliate={true}>` with `<AuthGuard requiredRoles={["AFFILIATE"]}>`.
2. Verify navigation and access to affiliate pages post-change.
3. Optionally add role-based component wrappers from user-context if needed: [src/contexts/user-context.tsx](src/contexts/user-context.tsx)

Important notes:
- AuthGuard currently supports requiredRoles, fallback, redirectTo.
- Keeping prop API consistent avoids confusion across role layouts.

## Environment Setup and Run
Last performed: 2025-09-16

Files to review:
- Scripts and ports: [package.json](package.json)
- Headers and CORS: [next.config.ts](next.config.ts)
- Docker build/run: [Dockerfile](Dockerfile), [docker-compose.yml](docker-compose.yml)
- TS config and aliases: [tsconfig.json](tsconfig.json)

Steps:
1. Create .env with NEXT_PUBLIC_API_BASE_URL and NEXTAUTH_SECRET.
2. Run dev: `npm run dev` on port 7500 or build/start on 7501.
3. Optionally, use docker-compose to run production container on 7501.
4. Verify /login flow against backend and redirection to /dashboard.

Important notes:
- Ensure reverse proxy forwards X-Forwarded-Host if applicable.
- Locale defaults to fa; consider negotiation if needed.