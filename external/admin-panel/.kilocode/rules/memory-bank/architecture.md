# Voopee Admin Panel — Architecture

## System Overview
Next.js 15 App Router application with:
- Authentication and session lifecycle via NextAuth v5 Credentials provider
- JWT access and refresh tokens managed server and client side
- RBAC enforced in middleware and client guards
- Internationalization using next-intl with fa as default and en fallback
- UI built with Tailwind v4 and shadcn style components
- API communication via a custom API client with token awareness

Key entry points:
- NextAuth configuration: [src/lib/auth.ts](src/lib/auth.ts)
- Route handlers for NextAuth: [src/app/api/auth/[...nextauth]/route.ts](src/app/api/auth/[...nextauth]/route.ts)
- RBAC Middleware: [src/middleware.ts](src/middleware.ts)
- Token refresh utilities: [src/lib/token-refresh.ts](src/lib/token-refresh.ts)
- Client session refresh hook: [src/hooks/use-token-refresh.ts](src/hooks/use-token-refresh.ts)
- API client with auto refresh: [src/lib/api-client.ts](src/lib/api-client.ts)
- i18n config and runtime messages: [src/i18n/request.ts](src/i18n/request.ts), [messages/fa/common.json](messages/fa/common.json)
- Providers and context: [src/components/providers.tsx](src/components/providers.tsx), [src/contexts/user-context.tsx](src/contexts/user-context.tsx)
- Role-based navigation: [src/lib/role-based-navigation.ts](src/lib/role-based-navigation.ts)
- Root layout: [src/app/layout.tsx](src/app/layout.tsx)

## Source Structure
- App Router pages and layouts
  - Admin routes and layout: [src/app/admin/layout.tsx](src/app/admin/layout.tsx), [src/app/admin/dashboard/page.tsx](src/app/admin/dashboard/page.tsx)
  - Affiliate routes and layout: [src/app/affiliate/layout.tsx](src/app/affiliate/layout.tsx), [src/app/affiliate/dashboard/page.tsx](src/app/affiliate/dashboard/page.tsx)
  - Login page: [src/app/login/page.tsx](src/app/login/page.tsx)
  - Dashboard router page: [src/app/dashboard/page.tsx](src/app/dashboard/page.tsx)
  - Forbidden page: [src/app/403/page.tsx](src/app/403/page.tsx)
- Admin feature modules
  - Discounts UI pages: [src/app/admin/dashboard/discounts/page.tsx](src/app/admin/dashboard/discounts/page.tsx), [src/app/admin/dashboard/generate-discount/page.tsx](src/app/admin/dashboard/generate-discount/page.tsx)
  - Discounts API wrapper: [src/lib/admin/discount-api.ts](src/lib/admin/discount-api.ts)
  - Discounts hooks: [src/hooks/admin/useDiscounts.ts](src/hooks/admin/useDiscounts.ts), [src/hooks/admin/useFilteredDiscounts.ts](src/hooks/admin/useFilteredDiscounts.ts)
  - Discounts UI components: [src/components/admin/discounts/DiscountContent.tsx](src/components/admin/discounts/DiscountContent.tsx)
- Shared UI primitives: [src/components/ui/button.tsx](src/components/ui/button.tsx), [src/components/ui/card.tsx](src/components/ui/card.tsx)

## Authentication Lifecycle
Primary logic: [src/lib/auth.ts](src/lib/auth.ts)
- CredentialsProvider expects phone, confirmation_code, device_id
- POST to external API /auth/login then fetches profile /users/profile
- Stores accessToken, refreshToken, roles, profile on token and session in NextAuth callbacks
- JWT callback refreshes tokens using [src/lib/token-refresh.ts](src/lib/token-refresh.ts) when expired or expiring soon
- Session callback hydrates session.user with profile and roles

Client-side helpers:
- Token refresh hook triggers NextAuth session update to execute JWT callback: [src/hooks/use-token-refresh.ts](src/hooks/use-token-refresh.ts)
- Debug component for token state: [src/components/token-status-debug.tsx](src/components/token-status-debug.tsx)

## RBAC and Route Protection
Middleware RBAC: [src/middleware.ts](src/middleware.ts)
- Protects admin and affiliate routes with toggles:
  - NEXT_PUBLIC_ENABLE_ADMIN_GUARD
  - NEXT_PUBLIC_ENABLE_ADMIN_ROLE_CHECK
- Retrieves NextAuth JWT via multiple cookie strategies
- Validates token expiry and decodes roles from access token when needed
- Adds header X-Token-Refresh-Needed for near-expiry tokens
- Redirects to /login on expiry or missing roles; redirects to /403 on insufficient role

Client guard: [src/components/auth-guard.tsx](src/components/auth-guard.tsx)
- Uses useSession and useTokenRefresh to ensure valid session
- Enforces requiredRoles when role check enabled
- Handles redirect to /403 or /login as needed

Role-based navigation:
- Compute home and dashboard target by roles: [src/lib/role-based-navigation.ts](src/lib/role-based-navigation.ts)
- Dashboard router page redirects to role dashboard: [src/app/dashboard/page.tsx](src/app/dashboard/page.tsx)

## Internationalization
Runtime config: [src/i18n/request.ts](src/i18n/request.ts)
- Default locale fa
- Messages dynamically imported by feature and role
Consumption:
- Root layout provides NextIntlClientProvider: [src/app/layout.tsx](src/app/layout.tsx)
- Components use useTranslations for keys e.g., admin dashboard: [src/app/admin/dashboard/page.tsx](src/app/admin/dashboard/page.tsx)
Message organization examples:
- Common: [messages/fa/common.json](messages/fa/common.json)
- Auth: [messages/fa/auth.json](messages/fa/auth.json)
- Admin discount: [messages/fa/admin/discount/discounts.json](messages/fa/admin/discount/discounts.json)

## API Client
Custom fetch wrapper aware of token lifecycle: [src/lib/api-client.ts](src/lib/api-client.ts)
- Reads session via getSession to get accessToken and refreshToken
- If token expired or near expiry, attempts refresh via [src/lib/token-refresh.ts](src/lib/token-refresh.ts)
- Retries 401 responses up to configured retries
- JSON helpers return typed responses and surface errors via AppApiError

## Providers and Context
- SessionProvider and RTL DirectionProvider in [src/components/providers.tsx](src/components/providers.tsx)
- User context derives user, roles, and token fields from session with utility role guards: [src/contexts/user-context.tsx](src/contexts/user-context.tsx)

## Admin Discounts Feature
Data layer:
- API methods: [src/lib/admin/discount-api.ts](src/lib/admin/discount-api.ts)
- Data hooks: [src/hooks/admin/useDiscounts.ts](src/hooks/admin/useDiscounts.ts)
UI layer:
- Page list: [src/app/admin/dashboard/discounts/page.tsx](src/app/admin/dashboard/discounts/page.tsx)
- Page generate form: [src/app/admin/dashboard/generate-discount/page.tsx](src/app/admin/dashboard/generate-discount/page.tsx)
Navigation:
- Admin menu: [src/components/admin/dashboard/admin-nav.tsx](src/components/admin/dashboard/admin-nav.tsx)

## Key Technical Decisions
- NextAuth JWT strategy with server-side refresh in jwt callback for reliability and centralization: [src/lib/auth.ts](src/lib/auth.ts)
- Middleware performs role extraction using either token.roles or decoded access token claims for resilience: [src/middleware.ts](src/middleware.ts)
- Client hook only triggers session update; refresh logic centralized in JWT callback: [src/hooks/use-token-refresh.ts](src/hooks/use-token-refresh.ts)
- i18n messages loaded per feature to keep bundles organized: [src/i18n/request.ts](src/i18n/request.ts)
- CORS headers for /api/auth and forwarding header for proxies set in Next config: [next.config.ts](next.config.ts)

## Critical Flows

### Login and Session Establishment
```mermaid
sequenceDiagram
  participant U as User
  participant L as /login
  participant NA as NextAuth
  participant API as External API

  U->>L: Enter phone and otp
  L->>NA: signIn credentials
  NA->>API: POST auth login
  API-->>NA: access_token refresh_token
  NA->>API: GET users profile
  API-->>NA: profile with roles
  NA-->>L: session established with tokens roles profile
  L->>U: Redirect to /dashboard
```

### Protected Route Access and Token Refresh
```mermaid
sequenceDiagram
  participant C as Client
  participant MW as Middleware
  participant NA as NextAuth
  participant API as External API

  C->>MW: Request /admin
  MW->>NA: getToken and check expiry
  alt expired
    MW-->>C: Redirect to /login expired true
  else roles ok
    MW-->>C: Next with X-Token-Refresh-Needed when near expiry
  end

  C->>NA: useTokenRefresh triggers update
  NA->>API: POST auth refresh token
  API-->>NA: new access refresh
  NA-->>C: updated session
```

## Environment and Runtime
- Ports: dev 7500, production 7501
  - Scripts: [package.json](package.json)
- Next config headers: [next.config.ts](next.config.ts)
- Dockerized build and run: [Dockerfile](Dockerfile), [docker-compose.yml](docker-compose.yml)

## Risks and Considerations
- Ensure NEXT_PUBLIC_API_BASE_URL and NEXTAUTH_SECRET configured in environments
- Access token decoding relies on jose or local helpers; malformed tokens should be handled defensively
- Middleware must avoid blocking public and static routes; current matcher excludes api and next internals: [src/middleware.ts](src/middleware.ts)
- Centralize error messages via i18n to avoid hardcoded strings