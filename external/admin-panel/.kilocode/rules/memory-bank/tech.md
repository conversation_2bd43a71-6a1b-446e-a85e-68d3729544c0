# Voopee Admin Panel — Tech

## Stack Overview
- Framework: Next.js 15 (App Router) [next.config.ts](next.config.ts)
- Language: TypeScript 5 [tsconfig.json](tsconfig.json)
- React: 19.1.0
- Auth: NextAuth v5 (Credentials provider) [src/lib/auth.ts](src/lib/auth.ts), [src/app/api/auth/[...nextauth]/route.ts](src/app/api/auth/[...nextauth]/route.ts)
- Security/JWT: jose 6.x
- i18n: next-intl 4.x [src/i18n/request.ts](src/i18n/request.ts)
- Styling: Tailwind CSS v4 via @tailwindcss/postcss [postcss.config.mjs](postcss.config.mjs)
- UI: shadcn-style components, Radix primitives [src/components/ui](src/components/ui)
- State/context: React Context for user data [src/contexts/user-context.tsx](src/contexts/user-context.tsx)
- Date libs: date-fns 4.x, date-fns-jalali 4.x
- Forms/Validation: react-hook-form + zod (zod present; schema usage to be expanded)
- Build/Run: Node 20 (Docker), Turbopack for dev/build [Dockerfile](Dockerfile), [docker-compose.yml](docker-compose.yml)

Key runtime utilities:
- API Client with token awareness: [src/lib/api-client.ts](src/lib/api-client.ts)
- Token refresh helpers: [src/lib/token-refresh.ts](src/lib/token-refresh.ts)
- Token lifecycle client hook: [src/hooks/use-token-refresh.ts](src/hooks/use-token-refresh.ts)
- RBAC Middleware: [src/middleware.ts](src/middleware.ts)

## Packages and Versions (from package.json)
- next: 15.5.2
- react: 19.1.0, react-dom: 19.1.0
- next-auth: ^5.0.0-beta.29
- next-intl: ^4.3.6
- jose: ^6.1.0
- date-fns: ^4.1.0, date-fns-jalali: ^4.1.0-0
- react-hook-form: ^7.62.0
- zod: ^3.25.76
- UI: @radix-ui/react-* (direction, label, navigation-menu, select), shadcn workflow
- Utilities: class-variance-authority, clsx, tailwind-merge

Dev/Tooling:
- typescript: ^5
- eslint: ^9 with next config [eslint.config.mjs](eslint.config.mjs)
- tailwindcss: ^4 with PostCSS plugin [postcss.config.mjs](postcss.config.mjs)
- shadcn CLI: ^3.1.0

## Environment Variables
- NEXT_PUBLIC_API_BASE_URL: Base URL for external NestJS API (used in auth/login, profile, refresh) [src/lib/auth.ts](src/lib/auth.ts), [src/lib/token-refresh.ts](src/lib/token-refresh.ts), [src/lib/api-client.ts](src/lib/api-client.ts)
- NEXTAUTH_SECRET: Secret for NextAuth JWT/session encryption [src/lib/auth.ts](src/lib/auth.ts)
- NEXT_PUBLIC_ENABLE_ADMIN_GUARD: Toggle middleware guard [src/middleware.ts](src/middleware.ts)
- NEXT_PUBLIC_ENABLE_ADMIN_ROLE_CHECK: Toggle role verification [src/middleware.ts](src/middleware.ts)

Note: Configure these via .env for local dev and deployment environments.

## Scripts, Ports, Runtime
- Dev: npm run dev → Next dev with Turbopack on port 7500 [package.json](package.json)
- Build: npm run build → Next build with Turbopack
- Start: npm start → Next start on port 7501
- Docker: Multi-stage build on Node 20 Alpine, exposes 7501 [Dockerfile](Dockerfile)
- Compose: Maps 7501:7501, NODE_ENV=production [docker-compose.yml](docker-compose.yml)

Reverse proxy/headers:
- Global X-Forwarded-Host header, and CORS headers for /api/auth [next.config.ts](next.config.ts)

## TypeScript Configuration
- Strict mode enabled
- Module resolution: bundler
- Path aliases: @/* → ./src/* [tsconfig.json](tsconfig.json)
- JSX: preserve
- Includes all ts/tsx plus next types

## Linting/Formatting
- ESLint flat config extending next/core-web-vitals and next/typescript [eslint.config.mjs](eslint.config.mjs)
- Ignores: node_modules, .next, out, build, next-env.d.ts

## Styling
- Tailwind v4 integrated via PostCSS plugin [postcss.config.mjs](postcss.config.mjs)
- shadcn-style primitives located under [src/components/ui](src/components/ui)
- RTL via Radix DirectionProvider in app providers [src/components/providers.tsx](src/components/providers.tsx)

## Internationalization Pattern
- next-intl server getRequestConfig with static locale "fa" currently [src/i18n/request.ts](src/i18n/request.ts)
- Messages structured by domain/feature:
  - Common: [messages/fa/common.json](messages/fa/common.json), [messages/en/common.json](messages/en/common.json)
  - Auth: [messages/fa/auth.json](messages/fa/auth.json), [messages/en/auth.json](messages/en/auth.json)
  - Admin/Affiliate feature sets under messages/fa and messages/en
- Provided to app via NextIntlClientProvider in root layout [src/app/layout.tsx](src/app/layout.tsx)

Note: Locale selection is currently hardcoded to fa; future enhancement to negotiate locale per request.

## Authentication and Token Lifecycle
- NextAuth CredentialsProvider exchanges phone + confirmation_code + device_id with external API for tokens, then retrieves profile and roles [src/lib/auth.ts](src/lib/auth.ts)
- Tokens and roles embedded into JWT and session in callbacks; jwt callback attempts refresh when expired/expiring via [src/lib/token-refresh.ts](src/lib/token-refresh.ts)
- Client hook triggers session update to run jwt callback and refresh when needed [src/hooks/use-token-refresh.ts](src/hooks/use-token-refresh.ts)
- Middleware validates tokens and roles for protected routes, adding X-Token-Refresh-Needed header when near expiry [src/middleware.ts](src/middleware.ts)

## API Client Usage Pattern
- Prefer json helpers: api.getJson/postJson/putJson/patchJson/deleteJson returning typed values [src/lib/api-client.ts](src/lib/api-client.ts)
- Automatically attaches Authorization from NextAuth session if present; attempts refresh when token is expired/expiring soon
- Retries 401 up to configured retries; errors normalized as AppApiError

## RBAC Pattern
- Server: Middleware identifies route type (admin/affiliate), checks env toggles, validates token expiry/roles, redirects accordingly [src/middleware.ts](src/middleware.ts)
- Client: AuthGuard enforces requiredRoles, redirects to /403 or /login; integrates token refresh hook [src/components/auth-guard.tsx](src/components/auth-guard.tsx)
- Role-based navigation helpers: getRoleBasedRedirectUrl/getHomeUrl [src/lib/role-based-navigation.ts](src/lib/role-based-navigation.ts)

## Constraints and Considerations
- External API availability is critical for login/profile/refresh; include timeouts and defensive error handling (implemented)
- Ensure environment variables are present in all environments (dev/prod/docker)
- JWT decoding via jose; malformed tokens should be handled defensively (middleware + jwt callback already defensive)
- Avoid blocking static/public routes in middleware; matcher excludes next internals and api by default [src/middleware.ts](src/middleware.ts)

## Testing and Observability
- Unit/e2e tests not present in repo; consider adding tests for:
  - Auth flows and token refresh
  - Middleware RBAC decisions
  - API client retry/refresh behavior
  - Discounts feature data hooks
- Extensive console logging present in auth/middleware/hooks; consider gating by NODE_ENV

## How-To Patterns
- Add a new protected page:
  1) Create page under /admin or /affiliate
  2) Wrap with AuthGuard in role layout if needed [src/app/admin/layout.tsx](src/app/admin/layout.tsx), [src/app/affiliate/layout.tsx](src/app/affiliate/layout.tsx)
  3) Add i18n keys in messages/fa and messages/en
- Add a new API wrapper + hook:
  1) Implement API function under [src/lib/admin](src/lib/admin) or feature folder using api.getJson/postJson
  2) Create a hook in [src/hooks](src/hooks) that calls the wrapper and manages state
  3) Use in a page/component and wire up filters/pagination as needed