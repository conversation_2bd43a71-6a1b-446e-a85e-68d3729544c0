# Voopee Admin Panel — Context

Last updated: 2025-09-16

## Current focus
- Initialize and codify the Memory Bank for continuity across sessions
- Document authentication, RBAC, token lifecycle, and i18n patterns
- Capture Admin Discounts feature flow and shared UI/provider structure

Key areas anchored to code:
- NextAuth credentials login and callbacks: [src/lib/auth.ts](src/lib/auth.ts)
- RBAC middleware and route protection: [src/middleware.ts](src/middleware.ts)
- Token refresh utilities and hook: [src/lib/token-refresh.ts](src/lib/token-refresh.ts), [src/hooks/use-token-refresh.ts](src/hooks/use-token-refresh.ts)
- API client with token awareness: [src/lib/api-client.ts](src/lib/api-client.ts)
- i18n runtime configuration: [src/i18n/request.ts](src/i18n/request.ts)
- Role-based navigation helpers: [src/lib/role-based-navigation.ts](src/lib/role-based-navigation.ts)
- Admin Discounts feature: [src/lib/admin/discount-api.ts](src/lib/admin/discount-api.ts), [src/hooks/admin/useDiscounts.ts](src/hooks/admin/useDiscounts.ts), [src/app/admin/dashboard/discounts/page.tsx](src/app/admin/dashboard/discounts/page.tsx)

## Recent changes (documentation)
- Added product description and goals: [.kilocode/rules/memory-bank/product.md](.kilocode/rules/memory-bank/product.md)
- Captured system architecture and critical flows: [.kilocode/rules/memory-bank/architecture.md](.kilocode/rules/memory-bank/architecture.md)
- Documented technologies, tooling, and patterns: [.kilocode/rules/memory-bank/tech.md](.kilocode/rules/memory-bank/tech.md)

## Observations and considerations
- Environment variables required at runtime:
  - NEXT_PUBLIC_API_BASE_URL, NEXTAUTH_SECRET, NEXT_PUBLIC_ENABLE_ADMIN_GUARD, NEXT_PUBLIC_ENABLE_ADMIN_ROLE_CHECK
- Locale is currently fixed to fa in i18n request config: [src/i18n/request.ts](src/i18n/request.ts)
- Middleware defensively retrieves token via multiple cookie names and decodes roles from access token as fallback: [src/middleware.ts](src/middleware.ts)
- Client hook triggers session update; refresh logic centralized in JWT callback: [src/hooks/use-token-refresh.ts](src/hooks/use-token-refresh.ts), [src/lib/auth.ts](src/lib/auth.ts)

Potential issue to review:
- Affiliate layout passes a prop not defined on AuthGuard
  - Usage: [src/app/affiliate/layout.tsx](src/app/affiliate/layout.tsx:15) uses `requireAffiliate={true}`
  - AuthGuard props: [src/components/auth-guard.tsx](src/components/auth-guard.tsx) supports `requiredRoles` but not `requireAffiliate`
  - Suggested alignment: change to `<AuthGuard requiredRoles={["AFFILIATE"]}>` or introduce a typed prop

## Next steps
- Optionally add tasks.md with repeatable workflows (e.g., "Add protected page", "Add API wrapper + hook")
- Validate env configuration across dev/prod and Docker
- Consider locale negotiation in i18n instead of static "fa"
- Review Affiliate AuthGuard usage and align props as noted above
- Share Memory Bank with stakeholders for verification