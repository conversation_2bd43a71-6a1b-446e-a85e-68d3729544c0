# Voopee Admin Panel — Product

## Why this project exists
Voopee Admin Panel provides a unified, role-aware management dashboard for two key personas:
- Administrators: manage users, products, discounts, and access analytics.
- Affiliates: track commissions, referrals, and performance.

The app connects to an external NestJS backend via REST, enforcing RBAC consistently in frontend and backend. It aims to streamline operational workflows, reduce manual errors, and ensure secure access separation.

## Problems it solves
- Fragmented tooling for admins and affiliates consolidated into one panel.
- Reliable authentication using OTP with secure session management and automatic token refresh.
- Consistent role-based access controls across routes and UI components.
- Localized experience (Persian default, English fallback) with RTL support.
- Usable UI with shadcn/tailwind baseline, reducing design inconsistency and implementation time.

## How it works (high-level)
- Authentication:
  - Phone + OTP + device_id are exchanged for access and refresh tokens via external API.
  - NextAuth CredentialsProvider manages sessions using JWT strategy.
  - Automatic refresh handled in NextAuth jwt callback and via client hook to keep tokens valid.
- Authorization:
  - Next.js middleware enforces guards for /admin and /affiliate paths, validating token expiry and roles.
  - Client-side components use AuthGuard and a UserContext to gate features and surfaces.
- Internationalization:
  - next-intl provides locale messages; default locale is fa (RTL) with en fallback.
  - Messages are structured per role/feature.
- UI:
  - Component-driven with shared shadcn/ui primitives and role-specific components.
  - Navigation adapts by role and uses helpers for home and dashboard routing.

## Primary users and jobs-to-be-done
- Administrators
  - Moderate users and products
  - Manage discount creation and export
  - Review dashboard metrics
- Affiliates
  - Monitor commissions and reports
  - Access referral links and performance summaries

## User experience goals
- Secure by default: all protected routes guarded; expired sessions are handled gracefully with clear messaging.
- Fast and responsive: optimistic UI where appropriate; loading states and pagination for data-heavy screens.
- Localized and accessible: full RTL layout, translated copy, focus-visible and keyboard navigation.
- Predictable navigation: role-based home and dashboard routing; clear 403 page with guidance to recover.

## Scope and non-goals
- In scope:
  - Admin dashboard, discount management (listing, filtering, export, generation).
  - Affiliate dashboard (commissions, basic reporting).
  - End-to-end OTP login flow and session lifecycle.
- Out of scope (current iteration):
  - Deep analytics tooling or BI-grade visualization.
  - Complex product catalogs or order management beyond described features.
  - Multi-tenant theming/white-labeling.

## Success metrics (initial)
- Authentication reliability: low rate of OTP/login errors attributable to client flow.
- Time-to-task: number of steps for an admin to generate and export discounts.
- RBAC correctness: zero incidents of cross-role access.
- i18n completeness: no placeholder or missing key errors in primary flows.

## Glossary
- Admin: A user with ADMIN role, can access /admin routes.
- Affiliate: A user with AFFILIATE role, can access /affiliate routes.
- RBAC: Role-Based Access Control, enforced in middleware and UI.
- OTP: One-Time Password used for login.

## References (source of truth in code)
- Auth and NextAuth configuration: [src/lib/auth.ts](src/lib/auth.ts)
- Middleware RBAC and token checks: [src/middleware.ts](src/middleware.ts)
- Token refresh utilities: [src/lib/token-refresh.ts](src/lib/token-refresh.ts)
- API client with auto-refresh: [src/lib/api-client.ts](src/lib/api-client.ts)
- i18n setup: [src/i18n/request.ts](src/i18n/request.ts)
- Role-based navigation helpers: [src/lib/role-based-navigation.ts](src/lib/role-based-navigation.ts)