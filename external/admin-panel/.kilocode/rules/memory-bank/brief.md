# Voopee Admin Panel — Project Summary

The **Voopee Admin Panel** is a web application that serves as a management dashboard for two distinct roles: **Administrators** and **Affiliates**. It is tightly integrated with a **NestJS backend**, communicates via an external API, and enforces **role-based access control (RBAC)** both in the frontend (middleware) and backend.

## Key Technologies

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom color scheme
- **UI Components**: shadcn/ui component library
- **Authentication**: NextAuth.js with JWT tokens
- **Internationalization**: next-intl for localization (primarily Persian/Farsi)
- **State Management**: React Context API
- **Date Handling**: date-fns and date-fns-jalali for Gregorian/Jalali calendar support
- **Form Handling**: react-hook-form with Zod validation

## Core Features

1. **Authentication & Session Management**
   - Custom login flow with phone number and OTP.
   - Managed by **NextAuth.js** using JWT access + refresh tokens.
   - Middleware enforces role-based protection for `/admin/*` and `/affiliate/*`.

2. **Role-Based Dashboards**
   - **Admin**: Manage users, products, discounts, sales analytics.
   - **Affiliate**: Track commissions, sales reports, and referrals.

3. **Internationalization**
   - Built with `next-intl`, default language **Persian (fa)** with English fallback.
   - Supports both **Gregorian** and **Jalali** calendars via `date-fns` and `date-fns-jalali`.
   - Translation files are organized by feature and role:
     - `messages/fa/common.json`: Common UI elements
     - `messages/fa/auth.json`: Authentication-related text
     - `messages/fa/admin/*.json`: Admin-specific translations
     - `messages/fa/affiliate/*.json`: Affiliate-specific translations

4. **UI & Styling**
   - Built with **React 19**, **TailwindCSS v4**, **Radix UI / shadcn/ui**.
   - Component-driven architecture with shared and role-specific components.

## Architecture & Conventions

The application follows a role-based architecture with distinct sections for administrators and affiliates:

1. **Role-Based Access Control**:
   - Admin users have access to `/admin/*` routes
   - Affiliate users have access to `/affiliate/*` routes
   - Generic dashboard at `/dashboard` that redirects based on roles

2. **Component Structure**:
   - UI components in `src/components/ui` (shadcn components)
   - Role-specific components in `src/components/admin` and `src/components/affiliate`
   - Shared components in `src/components`

3. **API Integration**
   - Uses a custom `src/lib/api-client` with automatic token refresh.
   - API base URL configurable via environment variables.
   - Backend API docs available at  `swagger.json` in root or at `http://localhost:3002/api/docs` (Swagger).

4. **Development Rules**
   - Follows **SOLID**, **Clean Code**, and **DRY** principles.
   - All user-facing text must use i18n messages.

## Code Structure

```
src/
├── app/                 # Next.js app router pages
│   ├── admin/          # Admin dashboard and features
│   ├── affiliate/      # Affiliate dashboard and features
│   ├── api/            # API routes (NextAuth.js)
│   ├── dashboard/      # Generic dashboard that redirects by role
│   ├── login/          # Authentication pages
│   └── 403/            # Forbidden access page
├── components/         # React components
│   ├── admin/          # Admin-specific components
│   ├── affiliate/      # Affiliate-specific components
│   ├── ui/             # Shared UI components (shadcn)
│   └── ...             # Shared components
├── contexts/           # React context providers
├── hooks/              # Custom React hooks
├── i18n/               # Internationalization setup
├── lib/                # Utility libraries
└── messages/          # Localization files
```