# Migration Guide: Persian (fa-IR) and RTL Support

## Overview
This migration adds full Persian language support and right-to-left (RTL) layout to the Next.js admin panel, including Jalali calendar integration.

## Prerequisites
- Next.js 15+
- Node.js 18+
- Persian font support (recommended: Vazirmatn or similar)

## Breaking Changes
- Default language changed from English to Persian (fa-IR)
- Layout direction changed to RTL globally
- Date formatting now uses Jalali calendar by default
- All hardcoded strings replaced with i18n keys

## Installation Steps

### 1. Install Required Packages
```bash
npm install next-i18next react-i18next date-fns-jalali i18next-browser-languagedetector i18next-http-backend
```

### 2. Configuration Files
- Added `next-i18next.config.js` for i18n configuration
- Updated `src/app/layout.tsx` with RTL support and Persian metadata
- Added `src/lib/i18n.ts` for client-side i18n setup
- Created `src/lib/date-utils.ts` for centralized date handling

### 3. Translation Files
Created translation files in `public/locales/`:
- `fa/common.json` - General translations
- `fa/auth.json` - Authentication messages
- `fa/dashboard.json` - Dashboard strings
- `en/` - English fallbacks

### 4. Styling Updates
- Updated `src/app/globals.css` with RTL-specific styles
- Added direction-aware layout rules
- Configured form elements for RTL

## Code Changes

### Component Updates
- `src/app/login/page.tsx`: Added i18n support, RTL-aware inputs
- `src/app/dashboard/page.tsx`: Localized all strings, added Jalali date display

### Date Handling
- All dates now use `formatDate()` from `@/lib/date-utils`
- Jalali calendar used by default for Persian locale
- Gregorian fallback available

### Authentication
- Login messages localized
- Error handling updated for Persian

## Testing
- Verify RTL layout renders correctly
- Test Persian text display
- Confirm Jalali date formatting
- Check form input directions (LTR for email/password)

## Rollback
To revert to English/LTR:
1. Change `defaultLocale` in `next-i18next.config.js` to 'en'
2. Remove `dir="rtl"` from `src/app/layout.tsx`
3. Update font subsets to 'latin' only
4. Remove RTL styles from `globals.css`

## Performance Considerations
- Translation files are loaded on-demand
- Date formatting optimized for Persian locale
- Minimal impact on bundle size

## Browser Support
- Modern browsers with RTL support
- Chrome 90+, Firefox 85+, Safari 14+
- Mobile browsers with RTL support